export interface Report {
  id: string;
  name: string;
  description: string;
  type: ReportType;
  category: ReportCategory;
  templateId?: string;
  
  // Configuration
  parameters: ReportParameter[];
  filters: ReportFilter[];
  columns: ReportColumn[];
  charts: ReportChart[];
  
  // Données
  data: any[];
  summary: ReportSummary;
  metadata: ReportMetadata;
  
  // Statut et permissions
  status: ReportStatus;
  visibility: ReportVisibility;
  isScheduled: boolean;
  schedule?: ReportSchedule;
  
  // Audit
  createdBy: string;
  createdAt: Date;
  updatedBy?: string;
  updatedAt?: Date;
  lastGeneratedAt?: Date;
  generationCount: number;
  
  // Export
  availableFormats: ExportFormat[];
  lastExportedAt?: Date;
  exportCount: number;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: ReportType;
  category: ReportCategory;
  
  // Configuration par défaut
  defaultParameters: ReportParameter[];
  defaultFilters: ReportFilter[];
  defaultColumns: ReportColumn[];
  defaultCharts: ReportChart[];
  
  // Métadonnées
  isSystem: boolean;
  isActive: boolean;
  tags: string[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
}

export interface ReportParameter {
  key: string;
  label: string;
  type: ParameterType;
  required: boolean;
  defaultValue?: any;
  options?: ParameterOption[];
  validation?: string;
  description?: string;
}

export interface ParameterOption {
  value: any;
  label: string;
  description?: string;
}

export interface ReportFilter {
  field: string;
  operator: FilterOperator;
  value: any;
  label: string;
  type: FilterType;
}

export interface ReportColumn {
  key: string;
  label: string;
  type: ColumnType;
  width?: number;
  sortable: boolean;
  filterable: boolean;
  format?: string;
  aggregation?: AggregationType;
  visible: boolean;
  order: number;
}

export interface ReportChart {
  id: string;
  title: string;
  type: ChartType;
  dataSource: string;
  xAxis: string;
  yAxis: string[];
  colors?: string[];
  options: ChartOptions;
  position: ChartPosition;
}

export interface ChartOptions {
  showLegend: boolean;
  showGrid: boolean;
  showLabels: boolean;
  height: number;
  width?: number;
  responsive: boolean;
  animation: boolean;
}

export interface ChartPosition {
  row: number;
  column: number;
  colspan: number;
  rowspan: number;
}

export interface ReportSummary {
  totalRecords: number;
  aggregations: { [key: string]: any };
  trends: ReportTrend[];
  insights: string[];
}

export interface ReportTrend {
  metric: string;
  value: number;
  previousValue?: number;
  change: number;
  changePercent: number;
  direction: TrendDirection;
}

export interface ReportMetadata {
  generatedAt: Date;
  generatedBy: string;
  executionTime: number;
  dataSource: string;
  recordCount: number;
  filters: string[];
  version: string;
}

export interface ReportSchedule {
  frequency: ScheduleFrequency;
  time: string; // HH:mm format
  dayOfWeek?: number; // 0-6 (Sunday-Saturday)
  dayOfMonth?: number; // 1-31
  recipients: string[];
  format: ExportFormat;
  isActive: boolean;
  lastRun?: Date;
  nextRun?: Date;
}

export enum ReportType {
  EMPLOYEE = 'EMPLOYEE',
  ATTENDANCE = 'ATTENDANCE',
  PAYROLL = 'PAYROLL',
  LEAVE = 'LEAVE',
  PERFORMANCE = 'PERFORMANCE',
  RECRUITMENT = 'RECRUITMENT',
  TRAINING = 'TRAINING',
  COMPLIANCE = 'COMPLIANCE',
  FINANCIAL = 'FINANCIAL',
  CUSTOM = 'CUSTOM'
}

export enum ReportCategory {
  OPERATIONAL = 'OPERATIONAL',
  STRATEGIC = 'STRATEGIC',
  COMPLIANCE = 'COMPLIANCE',
  FINANCIAL = 'FINANCIAL',
  ANALYTICS = 'ANALYTICS',
  DASHBOARD = 'DASHBOARD'
}

export enum ReportStatus {
  DRAFT = 'DRAFT',
  READY = 'READY',
  GENERATING = 'GENERATING',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR',
  SCHEDULED = 'SCHEDULED'
}

export enum ReportVisibility {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  SHARED = 'SHARED',
  DEPARTMENT = 'DEPARTMENT'
}

export enum ParameterType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  DATE_RANGE = 'DATE_RANGE',
  SELECT = 'SELECT',
  MULTI_SELECT = 'MULTI_SELECT',
  BOOLEAN = 'BOOLEAN',
  EMPLOYEE = 'EMPLOYEE',
  DEPARTMENT = 'DEPARTMENT'
}

export enum FilterOperator {
  EQUALS = 'EQUALS',
  NOT_EQUALS = 'NOT_EQUALS',
  CONTAINS = 'CONTAINS',
  NOT_CONTAINS = 'NOT_CONTAINS',
  STARTS_WITH = 'STARTS_WITH',
  ENDS_WITH = 'ENDS_WITH',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  GREATER_EQUAL = 'GREATER_EQUAL',
  LESS_EQUAL = 'LESS_EQUAL',
  BETWEEN = 'BETWEEN',
  IN = 'IN',
  NOT_IN = 'NOT_IN',
  IS_NULL = 'IS_NULL',
  IS_NOT_NULL = 'IS_NOT_NULL'
}

export enum FilterType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  SELECT = 'SELECT',
  BOOLEAN = 'BOOLEAN'
}

export enum ColumnType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  CURRENCY = 'CURRENCY',
  PERCENTAGE = 'PERCENTAGE',
  DATE = 'DATE',
  DATETIME = 'DATETIME',
  BOOLEAN = 'BOOLEAN',
  IMAGE = 'IMAGE',
  LINK = 'LINK'
}

export enum AggregationType {
  SUM = 'SUM',
  AVERAGE = 'AVERAGE',
  COUNT = 'COUNT',
  MIN = 'MIN',
  MAX = 'MAX',
  MEDIAN = 'MEDIAN'
}

export enum ChartType {
  BAR = 'BAR',
  LINE = 'LINE',
  PIE = 'PIE',
  DOUGHNUT = 'DOUGHNUT',
  AREA = 'AREA',
  SCATTER = 'SCATTER',
  RADAR = 'RADAR',
  GAUGE = 'GAUGE',
  FUNNEL = 'FUNNEL'
}

export enum TrendDirection {
  UP = 'UP',
  DOWN = 'DOWN',
  STABLE = 'STABLE'
}

export enum ScheduleFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  YEARLY = 'YEARLY'
}

export enum ExportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  CSV = 'CSV',
  JSON = 'JSON',
  HTML = 'HTML'
}

export interface ReportRequest {
  templateId?: string;
  name: string;
  description?: string;
  parameters: { [key: string]: any };
  filters: ReportFilter[];
  format?: ExportFormat;
  includeCharts: boolean;
  includeSummary: boolean;
}

export interface ReportExportRequest {
  reportId: string;
  format: ExportFormat;
  includeCharts: boolean;
  includeSummary: boolean;
  fileName?: string;
}

// Rapports prédéfinis pour RH
export interface EmployeeReport {
  totalEmployees: number;
  activeEmployees: number;
  newHires: number;
  terminations: number;
  byDepartment: { [key: string]: number };
  byPosition: { [key: string]: number };
  averageAge: number;
  genderDistribution: { [key: string]: number };
  tenureDistribution: { [key: string]: number };
}

export interface AttendanceReport {
  totalWorkingDays: number;
  averageAttendance: number;
  lateArrivals: number;
  earlyDepartures: number;
  absences: number;
  overtimeHours: number;
  byEmployee: AttendanceByEmployee[];
  trends: AttendanceTrend[];
}

export interface AttendanceByEmployee {
  employeeId: string;
  employeeName: string;
  workingDays: number;
  presentDays: number;
  absentDays: number;
  lateCount: number;
  overtimeHours: number;
  attendanceRate: number;
}

export interface AttendanceTrend {
  date: string;
  attendanceRate: number;
  totalEmployees: number;
  presentEmployees: number;
}

export interface PayrollReport {
  totalPayroll: number;
  averageSalary: number;
  medianSalary: number;
  totalBenefits: number;
  totalDeductions: number;
  byDepartment: PayrollByDepartment[];
  salaryDistribution: SalaryDistribution[];
  trends: PayrollTrend[];
}

export interface PayrollByDepartment {
  departmentId: string;
  departmentName: string;
  employeeCount: number;
  totalSalary: number;
  averageSalary: number;
  totalBenefits: number;
}

export interface SalaryDistribution {
  range: string;
  count: number;
  percentage: number;
}

export interface PayrollTrend {
  period: string;
  totalPayroll: number;
  averageSalary: number;
  employeeCount: number;
}
