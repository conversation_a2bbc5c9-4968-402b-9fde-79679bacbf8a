/**
 * Interface for Address - Frontend
 */
export interface Address {
  id?: string;
  street: string;
  city: string;
  postalCode?: string;
  country: string;
  createdAt?: string; // Date as ISO string
  updatedAt?: string; // Date as ISO string
}

/**
 * Interface for Company Tax Settings - Frontend
 */
export interface CompanyTaxSettings {
  id?: string;
  incomeTaxRate: number;
  socialSecurityRate: number;
  unEmploymentInsuranceRate: number;
  healthInsuranceRate: number;
  pensionContributionRate: number;
  incomeTaxThreshold?: number;
  socialSecurityThreshold?: number;
  taxPaymentFrequency?: TaxPaymentFrequencyEnum;
  companyId?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface for Payroll Configuration - Frontend
 */
export interface PayrollConfiguration {
  id?: string;
  payrollCycle: PayrollCycleEnum;
  paymentDay: number;
  overtimeMultiplier: number;
  maxOvertimeHours?: number;
  bonusType?: BonusCalculationEnum;
  performanceBonusRate?: number;
  companyId?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface for Company - Frontend
 */
export interface Company {
  id?: string;
  companyName: string;
  email: string;
  phoneNumbers: string[];
  website?: string;
  logo?: string;
  officialName: string;
  taxIdentificationNumber: string;
  industry: string;
  description: string;
  createdAt?: string;
  updatedAt?: string;

  // Relations
  address?: Address;
  taxSettings?: CompanyTaxSettings;
  payrollConfiguration?: PayrollConfiguration;
  employees?: Employee[];
  departments?: Department[];
  documents?: Document[];
  jobOffers?: JobOffer[];
}

/**
 * Interface for Employee - Frontend
 */
export interface Employee {
  id: string;
  hireDate: string;
  position?: Position;
  department?: Department;
  user: {
    id: string;
    email: string;
    role: RoleEnum;
    profile?: {
      firstName: string;
      lastName: string;
      avatar?: string;
    };
  };
  companyId: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface for Department - Frontend
 */
export interface Department {
  id: string;
  departmentName: string;
  description?: string;
  companyId: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface for Position - Frontend
 */
export interface Position {
  id: string;
  positionTitle: string;
  description?: string;
  departmentId: string;
  requiredSkills: string[];
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface for Document - Frontend
 */
export interface Document {
  id: string;
  name: string;
  type: string;
  url: string;
  description?: string;
  uploadedAt: string;
  companyId?: string;
  employeeId?: string;
  userId?: string;
}

// department.dto.ts
export interface CreateDepartmentDto {
  departmentName: string;
  description?: string;
}

export interface UpdateDepartmentDto {
  departmentName?: string;
  description?: string;
}

export interface DepartmentResponseDto {
  id: string;
  departmentName: string;
  description?: string;
  companyId: string;
  positions: PositionResponseDto[];
}

export interface CreatePositionDto {
  positionTitle: string;
  description?: string;
  requiredSkills?: string[];
}

export interface UpdatePositionDto {
  positionTitle?: string;
  description?: string;
  requiredSkills?: string[];
}

export interface PositionResponseDto {
  id: string;
  positionTitle: string;
  description?: string;
  departmentId: string;
  requiredSkills: string[];
}

/**
 * Interface for Job Offer - Frontend
 */
export interface JobOffer {
  id: string;
  title: string;
  description: string;
  publishDate: string;
  expirationDate: string;
  companyId: string;
  company?: Company;
  createdAt?: string;
  updatedAt?: string;
}

// Enums (à importer ou définir)
export enum TaxPaymentFrequencyEnum {
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ANNUALLY = 'ANNUALLY',
}

export enum PayrollCycleEnum {
  MONTHLY = 'MONTHLY',
  SEMI_MONTHLY = 'SEMI_MONTHLY',
  BI_WEEKLY = 'BI_WEEKLY',
  WEEKLY = 'WEEKLY',
}

export enum BonusCalculationEnum {
  PERCENTAGE_OF_SALARY = 'PERCENTAGE_OF_SALARY',
  FIXED_AMOUNT = 'FIXED_AMOUNT',
  PERFORMANCE_BASED = 'PERFORMANCE_BASED',
}

export enum RoleEnum {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN_HR = 'ADMIN_HR',
  ADMIN_RECRUITMENT = 'ADMIN_RECRUITMENT',
  OWNER = 'OWNER',
  EMPLOYEE = 'EMPLOYEE',
}
