export interface AddressDto {
  street: string;
  city: string;
  postalCode: string;
  country: string;
}

export enum RoleEnum {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  EMPLOYEE = 'EMPLOYEE',
  // Ajoutez d'autres rôles selon vos besoins
}

export interface CreateEmployeeDto {
  firstName: string;
  lastName: string;
  email: string;
  role?: RoleEnum;
  birthDate: Date;
  hireDate: Date;
  departmentId: string;
  positionId: string;
  address?: AddressDto;
  companyId?: string;
}

export interface UpdateEmployeeDto {
  firstName?: string;
  lastName?: string;
  email?: string;
  role?: RoleEnum;
  birthDate?: Date;
  hireDate?: Date;
  departmentId?: string;
  positionId?: string;
  address?: AddressDto;
}

export interface EmployeeResponseDto {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: RoleEnum;
  birthDate: Date;
  hireDate: Date;
  departmentId: string;
  positionId: string;
  address?: AddressDto;
  companyId: string;
  createdAt: Date;
  updatedAt: Date;
}
