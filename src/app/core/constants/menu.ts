import { MenuItem } from '../models/menu.model';

export class Menu {
  public static pages: MenuItem[] = [
    {
      group: 'Général',
      separator: false,
      items: [
        {
          icon: 'assets/icons/heroicons/outline/office-building.svg',
          label: 'Départements',
          route: '/dashboard/departments',
        },
        {
          icon: 'assets/icons/heroicons/outline/users.svg',
          label: 'Employés',
          route: '/dashboard/employees',
          children: [
            { label: 'Liste des employés', route: '/dashboard/employees' },
          ],
        },
        {
          icon: 'assets/icons/heroicons/outline/clock.svg',
          label: 'Temps',
          children: [
            { label: '<PERSON>uil<PERSON> de temps', route: '/dashboard/timesheets' },
            { label: 'Congés', route: '/dashboard/leaves' },
          ],
        },
        {
          icon: 'assets/icons/heroicons/outline/cash.svg',
          label: 'Paie',
          children: [
            { label: 'Salaires', route: '/dashboard/salaries' },
            { label: 'Fiches de paie', route: '/dashboard/payslips' },
          ],
        },
        {
          icon: 'assets/icons/heroicons/outline/document-text.svg',
          label: 'Contrats',
          children: [
            { label: 'Gestion des contrats', route: '/dashboard/contracts' },
            { label: 'Templates', route: '/dashboard/contracts/templates' },
          ],
        },
        {
          icon: 'assets/icons/heroicons/outline/folder.svg',
          label: 'Documents',
          children: [
            { label: 'Bibliothèque', route: '/dashboard/documents' },
            { label: 'Templates', route: '/dashboard/documents/templates' },
            { label: 'Approbations', route: '/dashboard/documents/approvals' },
          ],
        },

        {
          icon: 'assets/icons/heroicons/outline/search.svg',
          label: 'Recrutement',
          children: [
            {
              label: "Offres d'emploi",
              route: '/dashboard/recruitment/job-offers',
            },
            {
              label: 'Candidatures',
              route: '/dashboard/recruitment/applications',
            },
          ],
        },
        {
          icon: 'assets/icons/heroicons/outline/chart-pie.svg',
          label: 'Rapports',
          children: [
            { label: 'Tableau de bord', route: '/dashboard/reports' },
            { label: 'Rapports RH', route: '/dashboard/reports/hr' },
            { label: 'Analytics', route: '/dashboard/reports/analytics' },
          ],
        },
      ],
    },
    {
      group: 'Config',
      separator: false,
      items: [
        {
          icon: 'assets/icons/heroicons/outline/cog.svg',
          label: 'Paramètres',
          route: '/dashboard/settings',
        },
      ],
    },
  ];
}
