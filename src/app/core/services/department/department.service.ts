// department.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import {
  CreateDepartmentDto,
  DepartmentResponseDto,
  UpdateDepartmentDto,
} from '../../models/company.model';

@Injectable({
  providedIn: 'root',
})
export class DepartmentService {
  private apiUrl = `${environment.API_PREFIX}/companies`;

  constructor(private http: HttpClient) {}

  createDepartment(
    companyId: string,
    data: CreateDepartmentDto
  ): Observable<DepartmentResponseDto> {
    return this.http.post<DepartmentResponseDto>(
      `${this.apiUrl}/${companyId}/departments`,
      data
    );
  }

  getAllDepartments(companyId: string): Observable<DepartmentResponseDto[]> {
    return this.http.get<DepartmentResponseDto[]>(
      `${this.apiUrl}/${companyId}/departments`
    );
  }

  getDepartmentById(
    companyId: string,
    id: string
  ): Observable<DepartmentResponseDto> {
    return this.http.get<DepartmentResponseDto>(
      `${this.apiUrl}/${companyId}/departments/${id}`
    );
  }

  updateDepartment(
    companyId: string,
    id: string,
    data: UpdateDepartmentDto
  ): Observable<DepartmentResponseDto> {
    return this.http.put<DepartmentResponseDto>(
      `${this.apiUrl}/${companyId}/departments/${id}`,
      data
    );
  }

  deleteDepartment(companyId: string, id: string): Observable<void> {
    return this.http.delete<void>(
      `${this.apiUrl}/${companyId}/departments/${id}`
    );
  }
}
