import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import {
  Contract,
  ContractTemplate,
  ContractStats,
  ContractFilter,
  ContractFormData,
  ContractType,
  ContractStatus,
  VariableType,
  AIContractRequest,
  AIContractResponse,
} from '../../models/contract.model';

@Injectable({
  providedIn: 'root',
})
export class ContractService {
  private contractsSubject = new BehaviorSubject<Contract[]>([]);
  private templatesSubject = new BehaviorSubject<ContractTemplate[]>([]);

  public contracts$ = this.contractsSubject.asObservable();
  public templates$ = this.templatesSubject.asObservable();

  constructor() {
    this.loadMockData();
  }

  // Gestion des contrats
  getContracts(filter?: ContractFilter): Observable<Contract[]> {
    let contracts = this.contractsSubject.value;

    if (filter) {
      contracts = this.applyFilter(contracts, filter);
    }

    return of(contracts);
  }

  getContractById(id: string): Observable<Contract | null> {
    const contract = this.contractsSubject.value.find((c) => c.id === id);
    return of(contract || null);
  }

  createContract(contractData: ContractFormData): Observable<Contract> {
    const newContract: Contract = {
      id: this.generateId(),
      ...contractData,
      status: ContractStatus.DRAFT,
      content: '',
      signedByEmployee: false,
      signedByEmployer: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'current-user-id',
      version: 1,
      isActive: true,
    };

    const contracts = [...this.contractsSubject.value, newContract];
    this.contractsSubject.next(contracts);

    return of(newContract);
  }

  updateContract(id: string, updates: Partial<Contract>): Observable<Contract> {
    const contracts = this.contractsSubject.value.map((contract) =>
      contract.id === id
        ? {
            ...contract,
            ...updates,
            updatedAt: new Date(),
            version: contract.version + 1,
          }
        : contract
    );

    this.contractsSubject.next(contracts);
    const updatedContract = contracts.find((c) => c.id === id)!;

    return of(updatedContract);
  }

  deleteContract(id: string): Observable<boolean> {
    const contracts = this.contractsSubject.value.filter((c) => c.id !== id);
    this.contractsSubject.next(contracts);
    return of(true);
  }

  duplicateContract(id: string): Observable<Contract> {
    const originalContract = this.contractsSubject.value.find(
      (c) => c.id === id
    );
    if (!originalContract) {
      throw new Error('Contract not found');
    }

    const duplicatedContract: Contract = {
      ...originalContract,
      id: this.generateId(),
      title: `${originalContract.title} (Copie)`,
      status: ContractStatus.DRAFT,
      signedDate: undefined,
      signedByEmployee: false,
      signedByEmployer: false,
      employeeSignature: undefined,
      employerSignature: undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1,
    };

    const contracts = [...this.contractsSubject.value, duplicatedContract];
    this.contractsSubject.next(contracts);

    return of(duplicatedContract);
  }

  // Gestion des templates
  getTemplates(): Observable<ContractTemplate[]> {
    return this.templates$;
  }

  getTemplateById(id: string): Observable<ContractTemplate | null> {
    const template = this.templatesSubject.value.find((t) => t.id === id);
    return of(template || null);
  }

  createTemplate(
    template: Omit<
      ContractTemplate,
      'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'usageCount'
    >
  ): Observable<ContractTemplate> {
    const newTemplate: ContractTemplate = {
      ...template,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'current-user-id',
      usageCount: 0,
    };

    const templates = [...this.templatesSubject.value, newTemplate];
    this.templatesSubject.next(templates);

    return of(newTemplate);
  }

  updateTemplate(
    id: string,
    updates: Partial<ContractTemplate>
  ): Observable<ContractTemplate> {
    const templates = this.templatesSubject.value.map((template) =>
      template.id === id
        ? {
            ...template,
            ...updates,
            updatedAt: new Date(),
            version: template.version + 1,
          }
        : template
    );

    this.templatesSubject.next(templates);
    const updatedTemplate = templates.find((t) => t.id === id)!;

    return of(updatedTemplate);
  }

  deleteTemplate(id: string): Observable<boolean> {
    const templates = this.templatesSubject.value.filter((t) => t.id !== id);
    this.templatesSubject.next(templates);
    return of(true);
  }

  // Statistiques
  getContractStats(): Observable<ContractStats> {
    const contracts = this.contractsSubject.value;

    const stats: ContractStats = {
      total: contracts.length,
      active: contracts.filter((c) => c.status === ContractStatus.ACTIVE)
        .length,
      pending: contracts.filter(
        (c) => c.status === ContractStatus.PENDING_SIGNATURE
      ).length,
      expired: contracts.filter((c) => c.status === ContractStatus.EXPIRED)
        .length,
      byType: this.getStatsByType(contracts),
      byStatus: this.getStatsByStatus(contracts),
      recentContracts: contracts
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 5),
      expiringContracts: contracts
        .filter(
          (c) =>
            c.endDate &&
            c.endDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        )
        .slice(0, 5),
    };

    return of(stats);
  }

  // Fonctionnalités IA (préparation pour l'avenir)
  generateContractWithAI(
    request: AIContractRequest
  ): Observable<AIContractResponse> {
    // Simulation d'une réponse IA - à remplacer par un vrai service
    const mockResponse: AIContractResponse = {
      generatedContent: `<h1>Contrat de Travail - ${request.contractType}</h1>
        <p>Entre l'entreprise [COMPANY_NAME] et [EMPLOYEE_NAME]...</p>
        <p>Poste: ${request.position}</p>
        <p>Salaire: ${request.salary} €</p>
        <p>Date de début: [START_DATE]</p>`,
      suggestedVariables: [
        {
          key: 'COMPANY_NAME',
          label: "Nom de l'entreprise",
          type: VariableType.TEXT,
          required: true,
        },
        {
          key: 'EMPLOYEE_NAME',
          label: "Nom de l'employé",
          type: VariableType.TEXT,
          required: true,
        },
        {
          key: 'START_DATE',
          label: 'Date de début',
          type: VariableType.DATE,
          required: true,
        },
      ],
      confidence: 0.85,
      suggestions: [
        "Ajouter une clause de période d'essai",
        'Inclure les avantages sociaux',
        'Préciser les conditions de télétravail',
      ],
      warnings: [
        'Vérifier la conformité avec le code du travail',
        "S'assurer que le salaire respecte le SMIC",
      ],
    };

    return of(mockResponse);
  }

  // Méthodes utilitaires privées
  private applyFilter(
    contracts: Contract[],
    filter: ContractFilter
  ): Contract[] {
    return contracts.filter((contract) => {
      if (filter.search && !this.matchesSearch(contract, filter.search))
        return false;
      if (filter.contractType && contract.contractType !== filter.contractType)
        return false;
      if (filter.status && contract.status !== filter.status) return false;
      if (filter.employeeId && contract.employeeId !== filter.employeeId)
        return false;
      if (
        filter.isActive !== undefined &&
        contract.isActive !== filter.isActive
      )
        return false;
      return true;
    });
  }

  private matchesSearch(contract: Contract, search: string): boolean {
    const searchLower = search.toLowerCase();
    const titleMatch = contract.title.toLowerCase().includes(searchLower);
    const firstNameMatch =
      contract.employee?.user?.profile?.firstName
        ?.toLowerCase()
        .includes(searchLower) ?? false;
    const lastNameMatch =
      contract.employee?.user?.profile?.lastName
        ?.toLowerCase()
        .includes(searchLower) ?? false;

    return titleMatch || firstNameMatch || lastNameMatch;
  }

  private getStatsByType(contracts: Contract[]): {
    [key in ContractType]: number;
  } {
    const stats = {} as { [key in ContractType]: number };
    Object.values(ContractType).forEach((type) => {
      stats[type] = contracts.filter((c) => c.contractType === type).length;
    });
    return stats;
  }

  private getStatsByStatus(contracts: Contract[]): {
    [key in ContractStatus]: number;
  } {
    const stats = {} as { [key in ContractStatus]: number };
    Object.values(ContractStatus).forEach((status) => {
      stats[status] = contracts.filter((c) => c.status === status).length;
    });
    return stats;
  }

  private generateId(): string {
    return 'contract_' + Math.random().toString(36).substr(2, 9);
  }

  private loadMockData(): void {
    // Données de test - à remplacer par de vraies données
    const mockContracts: Contract[] = [
      {
        id: 'contract_1',
        employeeId: 'emp_1',
        contractType: ContractType.CDI,
        title: 'Contrat Développeur Senior',
        startDate: new Date('2024-01-15'),
        salary: 55000,
        currency: 'EUR',
        workingHours: 35,
        status: ContractStatus.ACTIVE,
        content: '<h1>Contrat de Travail CDI</h1><p>Développeur Senior...</p>',
        variables: [],
        signedByEmployee: true,
        signedByEmployer: true,
        signedDate: new Date('2024-01-10'),
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-01-10'),
        createdBy: 'hr_manager',
        version: 1,
        isActive: true,
      },
    ];

    const mockTemplates: ContractTemplate[] = [
      {
        id: 'template_1',
        name: 'Template CDI Standard',
        description: 'Template standard pour les contrats CDI',
        contractType: ContractType.CDI,
        content:
          '<h1>Contrat de Travail à Durée Indéterminée</h1><p>Entre [COMPANY_NAME] et [EMPLOYEE_NAME]...</p>',
        variables: [
          {
            key: 'COMPANY_NAME',
            label: "Nom de l'entreprise",
            type: VariableType.TEXT,
            required: true,
          },
          {
            key: 'EMPLOYEE_NAME',
            label: "Nom de l'employé",
            type: VariableType.TEXT,
            required: true,
          },
        ],
        isDefault: true,
        isActive: true,
        category: 'Standard',
        tags: ['CDI', 'Standard'],
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        createdBy: 'system',
        version: 1,
        usageCount: 15,
      },
    ];

    this.contractsSubject.next(mockContracts);
    this.templatesSubject.next(mockTemplates);
  }
}
