import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class LeaveService {
  private apiUrl = `${environment.API_PREFIX}/companies`;

  constructor(private http: HttpClient) {}

  /**
   * Crée une nouvelle demande de congé.
   * @param leaveData - Données de la demande de congé.
   * @returns Un Observable contenant la réponse du serveur.
   */
  createLeave(leaveData: any): Observable<any> {
    return this.http.post(`${this.apiUrl}`, leaveData).pipe(
      catchError((error) => {
        console.error(
          'Erreur lors de la création de la demande de congé',
          error
        );
        return throwError(error);
      })
    );
  }

  /**
   * R<PERSON>cup<PERSON> toutes les demandes de congé pour une entreprise donnée.
   * @param companyId - L'ID de l'entreprise.
   * @returns Un Observable contenant la liste des demandes de congé.
   */
  getLeaves(companyId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/${companyId}/leaves`).pipe(
      catchError((error) => {
        console.error(
          'Erreur lors de la récupération des demandes de congé',
          error
        );
        return throwError(error);
      })
    );
  }

  /**
   * Récupère toutes les demandes de congé pour un employé donné.
   * @param companyId - L'ID de l'entreprise.
   * @param employeeId - L'ID de l'employé.
   * @returns Un Observable contenant la liste des demandes de congé.
   */
  getEmployeeLeaves(companyId: string, employeeId: string): Observable<any> {
    return this.http
      .get(`${this.apiUrl}/${companyId}/employee/${employeeId}/leaves`)
      .pipe(
        catchError((error) => {
          console.error(
            'Erreur lors de la récupération des demandes de congé',
            error
          );
          return throwError(error);
        })
      );
  }

  /**
   * Récupère les détails d'une demande de congé par ID.
   * @param leaveId - L'ID de la demande de congé.
   * @returns Un Observable contenant les détails de la demande.
   */
  getLeaveById(leaveId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/leaves/${leaveId}`).pipe(
      catchError((error) => {
        console.error(
          `Erreur lors de la récupération de la demande de congé ${leaveId}`,
          error
        );
        return throwError(error);
      })
    );
  }

  /**
   * Met à jour une demande de congé existante.
   * @param leaveId - L'ID de la demande de congé.
   * @param leaveData - Les nouvelles données de la demande.
   * @returns Un Observable contenant la réponse du serveur.
   */
  updateLeave(leaveId: string, leaveData: any): Observable<any> {
    return this.http.patch(`${this.apiUrl}/leaves/${leaveId}`, leaveData).pipe(
      catchError((error) => {
        console.error(
          `Erreur lors de la mise à jour de la demande de congé ${leaveId}`,
          error
        );
        return throwError(error);
      })
    );
  }

  /**
   * Supprime une demande de congé par ID.
   * @param leaveId - L'ID de la demande de congé.
   * @returns Un Observable contenant la réponse du serveur.
   */
  deleteLeave(leaveId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/leaves/${leaveId}`).pipe(
      catchError((error) => {
        console.error(
          `Erreur lors de la suppression de la demande de congé ${leaveId}`,
          error
        );
        return throwError(error);
      })
    );
  }
}
