import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { EmployeeData } from 'src/app/modules/dashboard/models/employee';

export interface Salary {
  id: string;
  baseSalary: number;
  housingAllowance: number;
  transportAllowance: number;
  bonus: number;
  overtimeHours: number;
  overtimeRate: number;
  effectiveDate: string;
  employeeId: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Payslip {
  id?: string;
  month: number;
  year: number;
  grossSalary: number;
  taxDeductions: number;
  socialSecurity: number;
  otherDeductions: number;
  netSalary: number;
  employeeId: string;
  salaryId?: string;
  createdAt?: string;
  updatedAt?: string;
  employee?: Partial<EmployeeData> | null;
}

export interface CreateSalaryDto {
  employeeId: string;
  baseSalary: number;
  housingAllowance?: number;
  transportAllowance?: number;
  bonus?: number;
  overtimeHours?: number;
  overtimeRate?: number;
  effectiveDate: string;
}

export interface UpdateSalaryDto {
  baseSalary?: number;
  housingAllowance?: number;
  transportAllowance?: number;
  bonus?: number;
  overtimeHours?: number;
  overtimeRate?: number;
  effectiveDate?: string;
}

export interface GeneratePayslipDto {
  employeeId: string;
  year: number;
  month: number;
}

export interface UpdatePayslipDto {
  grossSalary?: number;
  taxDeductions?: number;
  socialSecurity?: number;
  otherDeductions?: number;
  netSalary?: number;
}

@Injectable({
  providedIn: 'root',
})
export class SalaryService {
  private apiUrl = `${environment.API_PREFIX}/salaries`;

  constructor(private http: HttpClient) {}

  // Salary CRUD Operations

  createSalary(salaryData: CreateSalaryDto): Observable<Salary> {
    return this.http
      .post<Salary>(this.apiUrl, salaryData)
      .pipe(catchError(this.handleError));
  }

  getAllSalariesForEmployee(employeeId: string): Observable<Salary[]> {
    return this.http
      .get<Salary[]>(`${this.apiUrl}/employee/${employeeId}`)
      .pipe(catchError(this.handleError));
  }

  getAllSalariesForCompany(companyId: string): Observable<Salary[]> {
    return this.http
      .get<Salary[]>(`${this.apiUrl}/company/${companyId}`)
      .pipe(catchError(this.handleError));
  }
  getCurrentSalary(employeeId: string): Observable<Salary | null> {
    return this.http.get<Salary>(`${this.apiUrl}/current/${employeeId}`).pipe(
      catchError((error) => {
        if (error.status === 404) {
          return throwError(
            () => new Error('No salary found for this employee')
          );
        }
        return this.handleError(error);
      })
    );
  }

  getSalaryById(id: string): Observable<Salary> {
    return this.http
      .get<Salary>(`${this.apiUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  updateSalary(id: string, updateData: UpdateSalaryDto): Observable<Salary> {
    return this.http
      .patch<Salary>(`${this.apiUrl}/${id}`, updateData)
      .pipe(catchError(this.handleError));
  }

  deleteSalary(id: string): Observable<void> {
    return this.http
      .delete<void>(`${this.apiUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  // Payslip Operations

  generatePayslip(data: GeneratePayslipDto): Observable<Payslip> {
    return this.http
      .post<Payslip>(`${this.apiUrl}/payslips/generate`, null, {
        params: {
          employeeId: data.employeeId,
          year: data.year.toString(),
          month: data.month.toString(),
        },
      })
      .pipe(catchError(this.handleError));
  }

  getPayslipById(id: string): Observable<Payslip> {
    return this.http
      .get<Payslip>(`${this.apiUrl}/payslips/${id}`)
      .pipe(catchError(this.handleError));
  }

  getEmployeePayslips(employeeId: string): Observable<Payslip[]> {
    return this.http
      .get<Payslip[]>(`${this.apiUrl}/payslips/employee/${employeeId}`)
      .pipe(catchError(this.handleError));
  }

  getCompanyPayslips(companyId: string): Observable<Payslip[]> {
    return this.http
      .get<Payslip[]>(`${this.apiUrl}/payslips/company/${companyId}`)
      .pipe(catchError(this.handleError));
  }

  updatePayslip(id: string, updateData: UpdatePayslipDto): Observable<Payslip> {
    return this.http
      .patch<Payslip>(`${this.apiUrl}/payslips/${id}`, updateData)
      .pipe(catchError(this.handleError));
  }

  deletePayslip(id: string): Observable<void> {
    return this.http
      .delete<void>(`${this.apiUrl}/payslips/${id}`)
      .pipe(catchError(this.handleError));
  }

  getPayslipByPeriod(
    employeeId: string,
    year: number,
    month: number
  ): Observable<Payslip | null> {
    return this.http
      .get<Payslip>(`${this.apiUrl}/payslips/period`, {
        params: {
          employeeId,
          year: year.toString(),
          month: month.toString(),
        },
      })
      .pipe(
        catchError((error) => {
          if (error.status === 404) {
            return throwError(
              () => new Error('Payslip not found for this period')
            );
          }
          return this.handleError(error);
        })
      );
  }

  // Utility Methods

  calculateTotalAllowances(salary: Salary): number {
    return (salary.housingAllowance || 0) + (salary.transportAllowance || 0);
  }

  calculateOvertimePay(salary: Salary): number {
    return (salary.overtimeHours || 0) * (salary.overtimeRate || 0);
  }

  calculateGrossSalary(salary: Salary): number {
    return (
      salary.baseSalary +
      this.calculateTotalAllowances(salary) +
      (salary.bonus || 0) +
      this.calculateOvertimePay(salary)
    );
  }

  getSalaryHistory(employeeId: string): Observable<Salary[]> {
    return this.http
      .get<Salary[]>(`${this.apiUrl}/history/${employeeId}`)
      .pipe(catchError(this.handleError));
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${
        error.error.message || error.message
      }`;
    }

    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
