import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import {
  DocumentModel,
  DocumentTemplate,
  DocumentFolder,
  DocumentStats,
  DocumentFilter,
  DocumentUploadRequest,
  BulkDocumentOperation,
  DocumentCategory,
  DocumentType,
  DocumentStatus,
  DocumentVisibility,
  DocumentAction,
  BulkOperation,
} from '../../models/document.model';

@Injectable({
  providedIn: 'root',
})
export class DocumentService {
  private documentsSubject = new BehaviorSubject<DocumentModel[]>([]);
  private templatesSubject = new BehaviorSubject<DocumentTemplate[]>([]);
  private foldersSubject = new BehaviorSubject<DocumentFolder[]>([]);

  public documents$ = this.documentsSubject.asObservable();
  public templates$ = this.templatesSubject.asObservable();
  public folders$ = this.foldersSubject.asObservable();

  constructor() {
    this.loadMockData();
  }

  // Gestion des documents
  getDocuments(filter?: DocumentFilter): Observable<DocumentModel[]> {
    let documents = this.documentsSubject.value;

    if (filter) {
      documents = this.applyFilter(documents, filter);
    }

    return of(documents);
  }

  getDocumentById(id: string): Observable<DocumentModel | null> {
    const document = this.documentsSubject.value.find((d) => d.id === id);
    return of(document || null);
  }

  uploadDocument(request: DocumentUploadRequest): Observable<DocumentModel> {
    const newDocument: DocumentModel = {
      id: this.generateId(),
      name: request.name,
      description: request.description,
      fileName: `${Date.now()}_${request.file.name}`,
      originalFileName: request.file.name,
      fileSize: request.file.size,
      mimeType: request.file.type,
      filePath: `/uploads/documents/${Date.now()}_${request.file.name}`,
      category: request.category,
      type: this.getDocumentTypeFromMime(request.file.type),
      status: request.approvalRequired
        ? DocumentStatus.PENDING_REVIEW
        : DocumentStatus.PUBLISHED,
      visibility: request.visibility,
      tags: request.tags,
      employeeId: request.employeeId,
      departmentId: request.departmentId,
      companyId: 'current-company-id',
      uploadedBy: 'current-user-id',
      uploadedAt: new Date(),
      version: 1,
      isActive: true,
      expirationDate: request.expirationDate,
      isConfidential: request.isConfidential,
      requiresSignature: request.requiresSignature,
      approvalRequired: request.approvalRequired,
      accessLog: [],
      downloadCount: 0,
      isArchived: false,
    };

    const documents = [...this.documentsSubject.value, newDocument];
    this.documentsSubject.next(documents);

    return of(newDocument);
  }

  updateDocument(
    id: string,
    updates: Partial<DocumentModel>
  ): Observable<DocumentModel> {
    const documents = this.documentsSubject.value.map((doc) =>
      doc.id === id
        ? {
            ...doc,
            ...updates,
            updatedAt: new Date(),
            version: doc.version + 1,
          }
        : doc
    );

    this.documentsSubject.next(documents);
    const updatedDocument = documents.find((d) => d.id === id)!;

    return of(updatedDocument);
  }

  deleteDocument(id: string): Observable<boolean> {
    const documents = this.documentsSubject.value.filter((d) => d.id !== id);
    this.documentsSubject.next(documents);
    return of(true);
  }

  downloadDocument(id: string): Observable<Blob> {
    // Simuler le téléchargement
    this.logDocumentAccess(id, DocumentAction.DOWNLOAD);

    // Incrémenter le compteur de téléchargement
    this.updateDocument(id, {
      downloadCount: (this.getDocumentById(id) as any).downloadCount + 1,
      lastAccessedAt: new Date(),
    });

    // Retourner un blob simulé
    const blob = new Blob(['Document content'], { type: 'application/pdf' });
    return of(blob);
  }

  // Gestion des dossiers
  getFolders(): Observable<DocumentFolder[]> {
    return this.folders$;
  }

  createFolder(
    folder: Omit<DocumentFolder, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>
  ): Observable<DocumentFolder> {
    const newFolder: DocumentFolder = {
      ...folder,
      id: this.generateId(),
      createdBy: 'current-user-id',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const folders = [...this.foldersSubject.value, newFolder];
    this.foldersSubject.next(folders);

    return of(newFolder);
  }

  // Gestion des templates
  getTemplates(): Observable<DocumentTemplate[]> {
    return this.templates$;
  }

  createTemplate(
    template: Omit<
      DocumentTemplate,
      'id' | 'createdAt' | 'updatedAt' | 'usageCount'
    >
  ): Observable<DocumentTemplate> {
    const newTemplate: DocumentTemplate = {
      ...template,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0,
    };

    const templates = [...this.templatesSubject.value, newTemplate];
    this.templatesSubject.next(templates);

    return of(newTemplate);
  }

  // Opérations en lot
  bulkOperation(operation: BulkDocumentOperation): Observable<boolean> {
    const documents = this.documentsSubject.value;
    let updatedDocuments = [...documents];

    operation.documentIds.forEach((docId) => {
      const docIndex = updatedDocuments.findIndex((d) => d.id === docId);
      if (docIndex !== -1) {
        switch (operation.operation) {
          case 'DELETE':
            updatedDocuments = updatedDocuments.filter((d) => d.id !== docId);
            break;
          case 'ARCHIVE':
            updatedDocuments[docIndex] = {
              ...updatedDocuments[docIndex],
              isArchived: true,
              archivedAt: new Date(),
              archivedBy: 'current-user-id',
            };
            break;
          case 'CHANGE_CATEGORY':
            if (operation.newCategory) {
              updatedDocuments[docIndex] = {
                ...updatedDocuments[docIndex],
                category: operation.newCategory,
              };
            }
            break;
          case 'CHANGE_VISIBILITY':
            if (operation.newVisibility) {
              updatedDocuments[docIndex] = {
                ...updatedDocuments[docIndex],
                visibility: operation.newVisibility,
              };
            }
            break;
          case 'ADD_TAGS':
            if (operation.newTags) {
              const existingTags = updatedDocuments[docIndex].tags;
              const newTags = [
                ...new Set([...existingTags, ...operation.newTags]),
              ];
              updatedDocuments[docIndex] = {
                ...updatedDocuments[docIndex],
                tags: newTags,
              };
            }
            break;
        }
      }
    });

    this.documentsSubject.next(updatedDocuments);
    return of(true);
  }

  // Statistiques
  getDocumentStats(): Observable<DocumentStats> {
    const documents = this.documentsSubject.value;

    const stats: DocumentStats = {
      total: documents.length,
      byCategory: this.getStatsByCategory(documents),
      byType: this.getStatsByType(documents),
      byStatus: this.getStatsByStatus(documents),
      totalSize: documents.reduce((sum, doc) => sum + doc.fileSize, 0),
      recentUploads: documents
        .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime())
        .slice(0, 5),
      expiringDocuments: documents
        .filter(
          (d) =>
            d.expirationDate &&
            d.expirationDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        )
        .slice(0, 5),
      pendingApprovals: documents
        .filter((d) => d.status === DocumentStatus.PENDING_REVIEW)
        .slice(0, 5),
      mostDownloaded: documents
        .sort((a, b) => b.downloadCount - a.downloadCount)
        .slice(0, 5),
    };

    return of(stats);
  }

  // Recherche avancée
  searchDocuments(
    query: string,
    filters?: DocumentFilter
  ): Observable<DocumentModel[]> {
    let documents = this.documentsSubject.value;

    // Recherche textuelle
    if (query) {
      const searchLower = query.toLowerCase();
      documents = documents.filter(
        (doc) =>
          doc.name.toLowerCase().includes(searchLower) ||
          doc.description?.toLowerCase().includes(searchLower) ||
          doc.tags.some((tag) => tag.toLowerCase().includes(searchLower)) ||
          doc.originalFileName.toLowerCase().includes(searchLower)
      );
    }

    // Appliquer les filtres
    if (filters) {
      documents = this.applyFilter(documents, filters);
    }

    return of(documents);
  }

  // Approbation de documents
  approveDocument(id: string): Observable<DocumentModel> {
    return this.updateDocument(id, {
      status: DocumentStatus.APPROVED,
      approvedBy: 'current-user-id',
      approvedAt: new Date(),
    });
  }

  rejectDocument(id: string, reason: string): Observable<DocumentModel> {
    return this.updateDocument(id, {
      status: DocumentStatus.REJECTED,
      rejectedBy: 'current-user-id',
      rejectedAt: new Date(),
      rejectionReason: reason,
    });
  }

  // Méthodes utilitaires privées
  private applyFilter(
    documents: DocumentModel[],
    filter: DocumentFilter
  ): DocumentModel[] {
    return documents.filter((doc) => {
      if (filter.search && !this.matchesSearch(doc, filter.search))
        return false;
      if (filter.category && doc.category !== filter.category) return false;
      if (filter.type && doc.type !== filter.type) return false;
      if (filter.status && doc.status !== filter.status) return false;
      if (filter.visibility && doc.visibility !== filter.visibility)
        return false;
      if (filter.employeeId && doc.employeeId !== filter.employeeId)
        return false;
      if (filter.departmentId && doc.departmentId !== filter.departmentId)
        return false;
      if (
        filter.isArchived !== undefined &&
        doc.isArchived !== filter.isArchived
      )
        return false;
      if (filter.dateFrom && doc.uploadedAt < filter.dateFrom) return false;
      if (filter.dateTo && doc.uploadedAt > filter.dateTo) return false;
      if (filter.tags && !filter.tags.some((tag) => doc.tags.includes(tag)))
        return false;
      return true;
    });
  }

  private matchesSearch(document: DocumentModel, search: string): boolean {
    const searchLower = search.toLowerCase();
    return (
      document.name.toLowerCase().includes(searchLower) ||
      (document.description?.toLowerCase().includes(searchLower) ?? false) ||
      document.originalFileName.toLowerCase().includes(searchLower) ||
      document.tags.some((tag) => tag.toLowerCase().includes(searchLower))
    );
  }

  private getDocumentTypeFromMime(mimeType: string): DocumentType {
    if (mimeType.includes('pdf')) return DocumentType.PDF;
    if (mimeType.includes('word') || mimeType.includes('document'))
      return DocumentType.WORD;
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet'))
      return DocumentType.EXCEL;
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation'))
      return DocumentType.POWERPOINT;
    if (mimeType.includes('image')) return DocumentType.IMAGE;
    if (mimeType.includes('video')) return DocumentType.VIDEO;
    if (mimeType.includes('audio')) return DocumentType.AUDIO;
    if (mimeType.includes('text')) return DocumentType.TEXT;
    if (mimeType.includes('zip') || mimeType.includes('archive'))
      return DocumentType.ARCHIVE;
    return DocumentType.OTHER;
  }

  private logDocumentAccess(documentId: string, action: DocumentAction): void {
    // Simuler l'enregistrement de l'accès
    console.log(`Document ${documentId} accessed with action: ${action}`);
  }

  private getStatsByCategory(documents: DocumentModel[]): {
    [key in DocumentCategory]: number;
  } {
    const stats = {} as { [key in DocumentCategory]: number };
    Object.values(DocumentCategory).forEach((category) => {
      stats[category] = documents.filter((d) => d.category === category).length;
    });
    return stats;
  }

  private getStatsByType(documents: DocumentModel[]): {
    [key in DocumentType]: number;
  } {
    const stats = {} as { [key in DocumentType]: number };
    Object.values(DocumentType).forEach((type) => {
      stats[type] = documents.filter((d) => d.type === type).length;
    });
    return stats;
  }

  private getStatsByStatus(documents: DocumentModel[]): {
    [key in DocumentStatus]: number;
  } {
    const stats = {} as { [key in DocumentStatus]: number };
    Object.values(DocumentStatus).forEach((status) => {
      stats[status] = documents.filter((d) => d.status === status).length;
    });
    return stats;
  }

  private generateId(): string {
    return 'doc_' + Math.random().toString(36).substr(2, 9);
  }

  private loadMockData(): void {
    // Données de test
    const mockDocuments: DocumentModel[] = [
      {
        id: 'doc_1',
        name: 'Règlement intérieur 2024',
        description: "Règlement intérieur de l'entreprise mis à jour",
        fileName: 'reglement_interieur_2024.pdf',
        originalFileName: 'reglement_interieur_2024.pdf',
        fileSize: 2048576,
        mimeType: 'application/pdf',
        filePath: '/uploads/documents/reglement_interieur_2024.pdf',
        category: DocumentCategory.POLICIES,
        type: DocumentType.PDF,
        status: DocumentStatus.PUBLISHED,
        visibility: DocumentVisibility.INTERNAL,
        tags: ['règlement', 'politique', '2024'],
        companyId: 'company_1',
        uploadedBy: 'hr_manager',
        uploadedAt: new Date('2024-01-15'),
        version: 1,
        isActive: true,
        isConfidential: false,
        requiresSignature: true,
        approvalRequired: false,
        accessLog: [],
        downloadCount: 25,
        isArchived: false,
      },
    ];

    const mockTemplates: DocumentTemplate[] = [
      {
        id: 'template_1',
        name: 'Template Contrat CDI',
        description: 'Template standard pour les contrats CDI',
        category: DocumentCategory.CONTRACTS,
        type: DocumentType.WORD,
        templatePath: '/templates/contrat_cdi.docx',
        variables: [],
        isActive: true,
        isDefault: true,
        createdBy: 'system',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        usageCount: 15,
      },
    ];

    const mockFolders: DocumentFolder[] = [
      {
        id: 'folder_1',
        name: 'Contrats',
        description: 'Dossier contenant tous les contrats',
        path: '/contrats',
        color: '#3B82F6',
        icon: 'document-text',
        isSystem: true,
        permissions: [],
        createdBy: 'system',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      },
    ];

    this.documentsSubject.next(mockDocuments);
    this.templatesSubject.next(mockTemplates);
    this.foldersSubject.next(mockFolders);
  }
}
