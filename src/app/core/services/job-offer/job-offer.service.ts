import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import {
  CreateJobOfferDto,
  JobOffer,
  UpdateJobOfferDto,
} from './job-offer.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class JobOffersService {
  private apiBaseUrl = `${environment.API_PREFIX}/companies`;

  constructor(private http: HttpClient) {}

  create(
    companyId: string,
    createJobOfferDto: CreateJobOfferDto
  ): Observable<JobOffer> {
    return this.http.post<JobOffer>(
      `${this.apiBaseUrl}/${companyId}/job-offers`,
      createJobOfferDto
    );
  }

  findAll(companyId?: string): Observable<JobOffer[]> {
    if (companyId) {
      return this.http.get<JobOffer[]>(
        `${this.apiBaseUrl}/${companyId}/job-offers`
      );
    }
    // Pour récupérer toutes les offres sans filtrer par entreprise
    // Note: Cette fonctionnalité doit être supportée par le backend
    return this.http.get<JobOffer[]>(`${environment.API_PREFIX}/job-offers`);
  }

  findOne(companyId: string, id: string): Observable<JobOffer> {
    return this.http.get<JobOffer>(
      `${this.apiBaseUrl}/${companyId}/job-offers/${id}`
    );
  }

  update(
    companyId: string,
    id: string,
    updateJobOfferDto: UpdateJobOfferDto
  ): Observable<JobOffer> {
    return this.http.patch<JobOffer>(
      `${this.apiBaseUrl}/${companyId}/job-offers/${id}`,
      updateJobOfferDto
    );
  }

  remove(companyId: string, id: string): Observable<void> {
    return this.http.delete<void>(
      `${this.apiBaseUrl}/${companyId}/job-offers/${id}`
    );
  }

  saveJobOffer(companyId: string, jobOfferId: string): Observable<void> {
    return this.http.post<void>(
      `${this.apiBaseUrl}/${companyId}/job-offers/${jobOfferId}/save`,
      {}
    );
  }

  getSavedJobOffers(companyId: string): Observable<JobOffer[]> {
    return this.http.get<JobOffer[]>(
      `${this.apiBaseUrl}/${companyId}/job-offers/saved`
    );
  }
}
