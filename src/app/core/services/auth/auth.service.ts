import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { JwtHelperService } from '@auth0/angular-jwt';
import { Observable, of, throwError } from 'rxjs';
import { catchError, switchMap, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { Store } from '@ngrx/store';
import { AppState } from '../../store/app.state';
import {
  loadUserFailure,
  loadUserSuccess,
} from '../../store/user/user.actions';
import {
  loadCompanyFailure,
  loadCompanySuccess,
} from '../../store/company/company.actions';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly ACCESS_TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private jwtHelper = new JwtHelperService();
  private apiUrl = `${environment.API_PREFIX}/auth`;
  private companyApiUrl = `${environment.API_PREFIX}/companies`;

  constructor(
    private http: HttpClient,
    private router: Router,
    private store: Store<AppState>
  ) {}

  signUp(userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
  }): Observable<any> {
    return this.http.post(`${this.apiUrl}/signup`, userData).pipe(
      tap((res: any) => {
        this.setTokens(res.accessToken, res.refreshToken);
      })
    );
  }

  /**
   * Authenticates the user and stores the received tokens.
   * @param credentials - The login credentials (email and password).
   * @returns An Observable of the server response.
   */
  login(credentials: { email: string; password: string }): Observable<any> {
    return this.http.post<unknown>(`${this.apiUrl}/signin`, credentials).pipe(
      tap((res: any) => {
        this.setTokens(res.accessToken, res.refreshToken);
      }),
      switchMap(() => this.fetchUserData()), // Attendre que fetchUserData() soit complété
      tap((user: any) => {
        this.fetchCompanyData(user.employeeData.companyId); // Récupérer la company après l'user
      }),
      tap(() => {
        this.router.navigate(['/']);
      }),

      catchError((error) => {
        console.error('Login failed', error);
        return throwError(error);
      })
    );
  }

  /**
   * Logs out the user by removing tokens and redirecting to the login page.
   */
  logout(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    this.router.navigate(['/login']);
  }

  /**
   * Checks if the user is authenticated.
   * @returns `true` if the user is authenticated, otherwise `false`.
   */
  isAuthenticated(): boolean {
    const token = this.getAccessToken();
    return !!token && !this.jwtHelper.isTokenExpired(token);
  }

  fetchUserData(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/me`).pipe(
      tap((user) => {
        this.store.dispatch(loadUserSuccess({ user })); // Dispatch user data to the store
      }),
      catchError((error) => {
        this.store.dispatch(loadUserFailure({ error })); // Dispatch error if something goes wrong
        return throwError(error);
      })
    );
  }

  fetchCompanyData(id: number): void {
    // API call to fetch company details
    this.http
      .get<any>(`${this.companyApiUrl}/${id}`)
      .pipe(
        tap((company) => {
          this.store.dispatch(loadCompanySuccess({ company })); // Dispatch company data to the store
        }),
        catchError((error) => {
          this.store.dispatch(loadCompanyFailure({ error })); // Dispatch error if something goes wrong
          return throwError(error);
        })
      )
      .subscribe();
  }

  /**
   * Refreshes the access token using the refresh token.
   * @returns An Observable containing the new tokens.
   */
  refreshToken(): Observable<any> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      this.logout();
      return throwError('No refresh token available');
    }

    return this.http
      .post('/api/auth/refresh-tokens', { refresh_token: refreshToken })
      .pipe(
        tap((res: any) => this.setTokens(res.access_token, res.refresh_token)),
        catchError((error) => {
          console.error('Refresh token failed', error);
          this.logout();
          return throwError(error);
        })
      );
  }

  /**
   * Stores tokens in localStorage.
   * @param accessToken - The access token.
   * @param refreshToken - The refresh token.
   */
  private setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  /**
   * Retrieves the access token from localStorage.
   * @returns The access token or `null` if it doesn't exist.
   */
  getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  /**
   * Retrieves the refresh token from localStorage.
   * @returns The refresh token or `null` if it doesn't exist.
   */
  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }
}
