import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import {
  CreateEmployeeDto,
  UpdateEmployeeDto,
} from '../../models/employee.model';

@Injectable({
  providedIn: 'root',
})
export class EmployeeService {
  private baseUrl = `${environment.API_PREFIX}/companies`;
  private authBaseUrl = `${environment.API_PREFIX}/auth`;

  constructor(private http: HttpClient) {}

  /**
   * Retrieves the list of employees for a specific company.
   * @param companyId - The ID of the company
   * @returns An Observable containing the employees list
   */
  getEmployees(companyId: string): Observable<any> {
    return this.http.get(`${this.baseUrl}/${companyId}/employees`).pipe(
      catchError((error) => {
        console.error('Error fetching employees', error);
        return throwError(error);
      })
    );
  }

  /**
   * Retrieves the details of an employee by ID.
   * @param companyId - The ID of the company
   * @param employeeId - The ID of the employee
   * @returns An Observable containing the employee details
   */
  getEmployeeById(companyId: string, employeeId: string): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/${companyId}/employees/${employeeId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error fetching employee ${employeeId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Creates a new employee in a company.
   * @param companyId - The ID of the company
   * @param employeeData - The employee data
   * @returns An Observable containing the server response
   */
  createEmployee(
    companyId: string,
    employeeData: CreateEmployeeDto
  ): Observable<any> {
    return this.http
      .post(`${this.baseUrl}/${companyId}/employees`, employeeData)
      .pipe(
        catchError((error) => {
          console.error('Error creating employee', error);
          return throwError(error);
        })
      );
  }

  /**
   * Updates an existing employee.
   * @param companyId - The ID of the company
   * @param employeeId - The ID of the employee
   * @param employeeData - The updated employee data
   * @returns An Observable containing the server response
   */
  updateEmployee(
    companyId: string,
    employeeId: string,
    employeeData: UpdateEmployeeDto
  ): Observable<any> {
    return this.http
      .patch(
        `${this.baseUrl}/${companyId}/employees/${employeeId}`,
        employeeData
      )
      .pipe(
        catchError((error) => {
          console.error(`Error updating employee ${employeeId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Retrieves statistics: total employees, new hires, resigned, and on leave.
   * @param companyId - The ID of the company
   * @param date - Optional date for statistics
   * @returns An Observable containing the statistics
   */
  getEmployeeStatistics(
    companyId: string,
    date?: Date
  ): Observable<{
    totalEmployees: number;
    newlyHired: number;
    resigned: number;
    onLeave: number;
  }> {
    const url = `${this.baseUrl}/${companyId}/employees/statistics${
      date ? `?date=${date.toISOString()}` : ''
    }`;

    return this.http
      .get<{
        totalEmployees: number;
        newlyHired: number;
        resigned: number;
        onLeave: number;
      }>(url)
      .pipe(
        catchError((error) => {
          console.error('Error fetching employee statistics', error);
          return throwError(error);
        })
      );
  }

  /**
   * Retrieves monthly statistics: new hires, resigned, and on leave for each month of a year.
   * @param companyId - The ID of the company
   * @param year - Optional year for statistics (defaults to current year)
   * @returns An Observable containing the monthly statistics
   */
  getMonthlyEmployeeStatistics(
    companyId: string,
    year?: number
  ): Observable<{
    monthlyStats: {
      month: number;
      newlyHired: number;
      resigned: number;
      onLeave: number;
    }[];
  }> {
    const targetYear = year || new Date().getFullYear();
    const url = `${this.baseUrl}/${companyId}/employees/statistics/monthly?year=${targetYear}`;

    return this.http
      .get<{
        monthlyStats: {
          month: number;
          newlyHired: number;
          resigned: number;
          onLeave: number;
        }[];
      }>(url)
      .pipe(
        catchError((error) => {
          console.error('Error fetching monthly employee statistics', error);
          return throwError(error);
        })
      );
  }
  /**
   * Generates a new password for a user and sends it by email
   * @param userId - The ID of the user
   * @returns An Observable containing the server response
   */
  generateAndSendPassword(userId: string): Observable<{ message: string }> {
    return this.http
      .post<{ message: string }>(
        `${this.authBaseUrl}/generate-password/${userId}`,
        {}
      )
      .pipe(
        catchError((error) => {
          console.error('Error generating password', error);
          return throwError(error);
        })
      );
  }
}
