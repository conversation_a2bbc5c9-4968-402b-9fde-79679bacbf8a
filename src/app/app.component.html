<div>
  <app-loader
    *ngIf="loader.isLoading$ | async"
    [message]="'Traitement en cours'"
    [subMessage]="'Veuillez patienter...'"
    [blockNavigation]="true"
    [overlay]="true"
    [isDark]="themeService.isDark"
  />
  <router-outlet></router-outlet>
  <ngx-sonner-toaster
    [theme]="themeService.isDark ? 'dark' : 'light'"
  ></ngx-sonner-toaster>

  <!-- Tour guidé -->
  <app-tour-welcome></app-tour-welcome>
  <app-tour-step></app-tour-step>
  <app-tour-help></app-tour-help>
</div>
