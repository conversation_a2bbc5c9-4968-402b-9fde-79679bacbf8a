import { Component, OnInit } from '@angular/core';
import { MenuService } from '../../services/menu.service';
import { NavbarMobileComponent } from './navbar-mobile/navbar-mobilecomponent';
import { ProfileMenuComponent } from './profile-menu/profile-menu.component';
import { NavbarMenuComponent } from './navbar-menu/navbar-menu.component';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { Company } from 'src/app/core/models/company.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
  standalone: true,
  imports: [
    AngularSvgIconModule,
    NavbarMenuComponent,
    ProfileMenuComponent,
    NavbarMobileComponent,
    NgIf,
    SlicePipe,
  ],
})
export class NavbarComponent implements OnInit {
  company!: Company;
  constructor(
    private menuService: MenuService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
    });
  }

  public toggleMobileMenu(): void {
    this.menuService.showMobileMenu = true;
  }
}
