<div class="relative bg-background">
  <div class="mx-auto px-5">
    <div class="flex items-center justify-between py-3.5 md:justify-start">
      <!-- Mobile Navigation Menu Button-->
      <div class="sm:order-1 md:hidden">
        <button
          (click)="toggleMobileMenu()"
          type="button"
          class="inline-flex items-center justify-center rounded-md bg-muted p-2 text-muted-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary hover:bg-muted-foreground hover:text-muted"
          aria-expanded="false"
        >
          <span class="sr-only">Open menu</span>
          <!-- Heroicon name: outline/menu -->
          <svg-icon
            src="assets/icons/heroicons/outline/menu.svg"
            [svgClass]="'h-6 w-6'"
          >
          </svg-icon>
        </button>
      </div>

      <!-- Logo -->
      <div
        class="flex items-center justify-start sm:order-2 md:mr-10 lg:hidden"
      >
        <a
          class="flex items-center justify-center rounded p-2 focus:outline-none focus:ring-1"
        >
          <!-- Logo de l'entreprise ou initiales de son nom officiel -->
          <ng-container *ngIf="company?.logo; else initials">
            <img
              [src]="company.logo"
              alt="Company Logo"
              class="h-8 w-8 rounded-full"
            />
          </ng-container>
          <ng-template #initials>
            <div
              class="h-8 w-8 flex items-center justify-center rounded-full bg-primary text-white"
            >
              {{ company.officialName | slice : 0 : 2 }}
              <!-- Affiche les 2 premières lettres -->
            </div>
          </ng-template>
        </a>
        <b class="hidden pl-3 text-sm font-bold text-foreground sm:block">{{
          company.officialName
        }}</b>
      </div>

      <!-- Desktop Menu -->
      <div class="hidden space-x-10 sm:order-3 md:flex">
        <app-navbar-menu></app-navbar-menu>
      </div>

      <!-- Profile menu -->
      <div class="items-center justify-end sm:order-4 md:flex md:flex-1 lg:w-0">
        <app-profile-menu data-tour="profile-menu"></app-profile-menu>
      </div>
    </div>
  </div>
  <!-- Mobile menu -->
  <app-navbar-mobile></app-navbar-mobile>
</div>
