import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './dashboard.component';
import { OverviewComponent } from './pages/overview/overview.component';
import { EmployeesComponent } from './pages/employees/employees.component';
import { EmployeeProfileComponent } from './pages/employees/employee-profile/employee-profile.component';
import { TimesheetListComponent } from './pages/timesheets/timesheet-list.component';
import { SalaryListComponent } from './pages/salaries/salary-list.component';
import { PayslipListComponent } from './pages/payslips/payslip-list.component';
import { DepartmentListComponent } from './pages/departments/department-list.component';
import { CompanyProfileComponent } from './pages/company/company-profile/company-profile.component';
import { LeaveListComponent } from './pages/leaves/leave-list.component';
import { WorkInProgressComponent } from 'src/app/shared/components/work-in-progress/work-in-progress.component';

const routes: Routes = [
  {
    path: '',
    component: DashboardComponent,
    children: [
      { path: '', redirectTo: 'overview', pathMatch: 'full' },
      { path: 'overview', component: OverviewComponent },

      // Employees
      { path: 'employees', component: EmployeesComponent },
      { path: 'employees/:id/profile', component: EmployeeProfileComponent },

      // Time Management
      { path: 'timesheets', component: TimesheetListComponent },

      // Leaves
      { path: 'leaves', component: LeaveListComponent },

      // Reports
      { path: 'reports', component: WorkInProgressComponent },

      // Payroll
      { path: 'salaries', component: SalaryListComponent },
      { path: 'payslips', component: PayslipListComponent },

      // Organization
      { path: 'departments', component: DepartmentListComponent },

      // Work in progress
      { path: 'work-in-progress', component: WorkInProgressComponent },

      // Contracts
      {
        path: 'contracts',
        loadChildren: () =>
          import('./pages/contracts/contracts.routes').then(
            (m) => m.CONTRACTS_ROUTES
          ),
      },

      // Documents
      {
        path: 'documents',
        loadChildren: () =>
          import('./pages/documents/documents.routes').then(
            (m) => m.DOCUMENTS_ROUTES
          ),
      },

      // Reports
      {
        path: 'reports',
        loadChildren: () =>
          import('./pages/reports/reports.routes').then(
            (m) => m.REPORTS_ROUTES
          ),
      },

      // Recruitment
      {
        path: 'recruitment',
        loadChildren: () =>
          import('../recruitment/recruitment.module').then(
            (m) => m.RecruitmentModule
          ),
      },

      // Settings
      { path: 'settings', component: CompanyProfileComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DashboardRoutingModule {}
