import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

@Component({
    selector: 'app-upcoming-events',
    templateUrl: './upcoming-events.component.html',
    imports: [CommonModule]
})
export class UpcomingEventsComponent {
  events = [
    {
      date: '15',
      month: 'Juin',
      title: "Rejoignez-nous pour célébrer l'anniversaire...",
      time: '10h00',
      category: 'Anniversaire',
    },
    {
      date: '22',
      month: 'Juin',
      title: "Bureau fermé à l'occasion du Jour...",
      time: '',
      category: 'Vacances',
    },
    {
      date: '10',
      month: '<PERSON><PERSON><PERSON>',
      title: 'Événement familial avec jeux, res...',
      time: '09h00 - 18h00',
      category: 'Pique-nique',
    },
    {
      date: '18',
      month: 'Ju<PERSON><PERSON>',
      title: 'Fête nationale - Do<PERSON> Ips<PERSON>',
      time: '',
      category: 'Vacances',
    },
    {
      date: '5',
      month: 'Août',
      title: 'Anniversaire du chiot de John - M...',
      time: '09h00',
      category: 'Anniversaire',
    },
    {
      date: '20',
      month: 'Août',
      title: 'Amet sed no dolor kasd - Et Dolor...',
      time: '16h00',
      category: 'Annonce',
    },
  ];
}
