<div class="rounded-lg bg-background p-4 shadow-md">
  <div class="mb-4 flex items-center justify-between">
    <h2 class="text-lg font-semibold text-foreground">Événements à venir</h2>
    <a href="#" class="text-sm text-blue-500 hover:underline">Tout voir</a>
  </div>
  <ul class="space-y-6">
    <li *ngFor="let event of events">
      <div class="flex items-start">
        <div class="flex flex-col items-center">
          <span class="text-sm font-medium text-gray-500">{{ event.date }}</span>
          <span class="text-xs text-gray-400">{{ event.month }}</span>
        </div>
        <div class="ml-4">
          <h3 class="w-48 truncate text-sm font-semibold text-muted-foreground">
            {{ event.title }}
          </h3>
          <div class="mt-1 flex items-center space-x-2">
            <span class="text-xs text-muted-foreground">{{ event.time }}</span>
            <span
              class="rounded-lg px-2 py-0.5 text-xs"
              [ngClass]="{
                'bg-red-100 text-red-600': event.category === 'Anniversaire',
                'bg-blue-100 text-blue-600': event.category === 'Vacances',
                'bg-green-100 text-green-600': event.category === 'Pique-nique',
                'bg-purple-100 text-purple-600': event.category === 'Annonce'
              }">
              {{ event.category }}
            </span>
          </div>
          <div class="mt-1 text-xs text-gray-500">...</div>
        </div>
      </div>
    </li>
  </ul>
</div>
