<td class="py-3 text-left flex items-center space-x-2">
  <div *ngIf="leave.employee.user.profile?.avatar; else defaultAvatar">
    <img
      [src]="leave.employee.user.profile?.avatar"
      alt="Avatar"
      class="w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
    />
  </div>
  <ng-template #defaultAvatar>
    <div
      class="w-10 h-10 flex items-center justify-center rounded-full text-white font-medium bg-gradient-to-r from-blue-500 to-indigo-600"
    >
      {{ (leave.employee.user?.profile?.firstName)![0] | uppercase }}
    </div>
  </ng-template>
  <span class="px-4 py-3 font-medium text-gray-800 dark:text-white"
    >{{ leave.employee.user.profile?.firstName }}
    {{ leave.employee.user.profile?.lastName }}</span
  >
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ leave.leaveType }}
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ getLeaveDays(leave.startDate, leave.endDate) }} jours
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">{{ leave.status }}</td>
