import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentUser } from 'src/app/core/store/user/user.selector';
import { Observable } from 'rxjs';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-overview-header',
  templateUrl: './overview-header.component.html',
  standalone: true,
  imports: [AsyncPipe],
})
export class OverviewHeaderComponent implements OnInit {
  user$!: Observable<any>;
  greetingMessage: string = '';
  greetingEmoji: string = '';

  constructor(private store: Store<AppState>) {}

  ngOnInit(): void {
    this.user$ = this.store.select(selectCurrentUser);
    this.updateGreetingMessage();
  }

  private updateGreetingMessage(): void {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) {
      this.greetingMessage = 'Bonne journée';
      this.greetingEmoji = '☀️';
    } else if (hour >= 12 && hour < 18) {
      this.greetingMessage = 'Bon après-midi';
      this.greetingEmoji = '🌤️';
    } else {
      this.greetingMessage = 'Bonne soirée';
      this.greetingEmoji = '🌙';
    }
  }
}
