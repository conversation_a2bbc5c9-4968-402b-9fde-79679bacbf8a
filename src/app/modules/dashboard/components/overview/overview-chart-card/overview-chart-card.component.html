<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
  <!-- Graphique principal existant -->
  <div class="flex-col rounded-lg bg-background px-8 py-8">
    <div class="mb-2 flex items-center justify-between">
      <div>
        <h3 class="text-lg font-bold text-foreground">
          Statistiques des employés
        </h3>
        <span class="text-sm text-muted-foreground">Aperçu mensuel</span>
      </div>
    </div>
    <apx-chart
      [series]="chartOptions.series!"
      [chart]="chartOptions.chart!"
      [xaxis]="chartOptions.xaxis!"
      [colors]="chartOptions.colors!"
      [tooltip]="chartOptions.tooltip!"
      [legend]="chartOptions.legend!"
    >
    </apx-chart>
  </div>
  <div class="flex-col rounded-lg bg-background p-6">
    <h3 class="text-lg font-bold text-foreground mb-2">
      Démographie des employés
    </h3>
    <apx-chart
      [series]="demographicsChartOptions.series!"
      [chart]="demographicsChartOptions.chart!"
      [labels]="demographicsChartOptions.labels!"
      [colors]="demographicsChartOptions.colors!"
    >
    </apx-chart>
  </div>
  <!-- Nouveaux graphiques -->

  <!-- Turnover vs Recrutement -->

  <!-- Démographie -->

  <!-- Compétences -->
  <div class="flex-col rounded-lg bg-background p-6">
    <h3 class="text-lg font-bold text-foreground mb-2">Compétences clés</h3>
    <apx-chart
      [series]="skillsChartOptions.series!"
      [chart]="skillsChartOptions.chart!"
      [xaxis]="skillsChartOptions.xaxis!"
      [colors]="skillsChartOptions.colors!"
    >
    </apx-chart>
  </div>

  <!-- Satisfaction -->
  <div class="flex-col rounded-lg bg-background p-6">
    <h3 class="text-lg font-bold text-foreground mb-2">
      Satisfaction employés
    </h3>
    <apx-chart
      [series]="satisfactionChartOptions.series!"
      [chart]="satisfactionChartOptions.chart!"
      [xaxis]="satisfactionChartOptions.xaxis!"
      [colors]="satisfactionChartOptions.colors!"
    >
    </apx-chart>
  </div>

  <!-- Salaires -->
  <div class="flex-col rounded-lg bg-background p-6">
    <h3 class="text-lg font-bold text-foreground mb-2">
      Distribution des salaires
    </h3>
    <apx-chart
      [series]="salaryDistributionChartOptions.series!"
      [chart]="salaryDistributionChartOptions.chart!"
      [colors]="salaryDistributionChartOptions.colors!"
    >
    </apx-chart>
  </div>

  <!-- Formation -->
  <div class="flex-col rounded-lg bg-background p-6">
    <h3 class="text-lg font-bold text-foreground mb-2">
      Formation des employés
    </h3>
    <apx-chart
      [series]="trainingChartOptions.series!"
      [chart]="trainingChartOptions.chart!"
      [xaxis]="trainingChartOptions.xaxis!"
      [colors]="trainingChartOptions.colors!"
    >
    </apx-chart>
  </div>
</div>
