import { Component, On<PERSON><PERSON>roy, OnInit, effect } from '@angular/core';
import { Subscription } from 'rxjs';
import { ThemeService } from 'src/app/core/services/theme.service';
import { ChartOptions } from '../../../../../shared/models/chart-options';
import { NgApexchartsModule } from 'ng-apexcharts';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { CommonModule } from '@angular/common';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: '[overview-chart-card]',
  templateUrl: './overview-chart-card.component.html',
  imports: [AngularSvgIconModule, NgApexchartsModule, CommonModule],
  standalone: true,
})
export class OverviewChartCardComponent implements On<PERSON>ni<PERSON>, OnDestroy {
  public chartOptions!: Partial<ChartOptions>;
  public turnoverChartOptions!: Partial<ChartOptions>;
  public demographicsChartOptions!: Partial<ChartOptions>;
  public skillsChartOptions!: Partial<ChartOptions>;
  public satisfactionChartOptions!: Partial<ChartOptions>;
  public salaryDistributionChartOptions!: Partial<ChartOptions>;
  public trainingChartOptions!: Partial<ChartOptions>;

  private subscription = new Subscription();
  private companyId!: string;
  private employees: any[] = [];

  constructor(
    private store: Store<AppState>,
    private themeService: ThemeService,
    private employeeService: EmployeeService
  ) {
    this.initMainChart();
    this.initAdditionalCharts();

    effect(() => {
      const theme = this.themeService.theme().mode;
      this.updateChartThemes(theme);
    });
  }

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.companyId = company.id!;
      this.loadAllEmployeeData();
    });
  }

  private loadAllEmployeeData(): void {
    this.subscription.add(
      this.employeeService.getEmployees(this.companyId).subscribe({
        next: (employees: any[]) => {
          this.employees = employees;
          this.updateAllCharts();
        },
        error: (error: any) => {
          console.error('Error loading employee data', error);
          this.setDummyData();
        },
      })
    );
  }

  private initMainChart(): void {
    const categories = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    this.chartOptions = {
      series: [
        { name: 'Nouveaux employés', data: [] },
        { name: 'Employés démissionnaires', data: [] },
        { name: 'Employés en congé', data: [] },
      ],
      chart: {
        type: 'bar',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      plotOptions: {
        bar: { horizontal: false, columnWidth: '65%', borderRadius: 4 },
      },
      dataLabels: { enabled: false },
      xaxis: {
        categories,
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      yaxis: { labels: { formatter: (val) => Math.round(val).toString() } },
      colors: ['#16A34A', '#DC2626', '#EA580C'],
      tooltip: { theme: 'light', y: { formatter: (val) => `${val} employés` } },
      legend: { position: 'top', horizontalAlign: 'left', offsetX: 0 },
      grid: {
        borderColor: '#e0e0e0',
        strokeDashArray: 5,
        padding: { left: 0, right: 0 },
      },
    };
  }

  private initAdditionalCharts(): void {
    // 1. Turnover vs Recrutement
    this.turnoverChartOptions = {
      series: [
        { name: 'Embauches', type: 'column', data: [] },
        { name: 'Départs', type: 'line', data: [] },
      ],
      chart: {
        height: 350,
        type: 'line',
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      colors: ['#16A34A', '#DC2626'],
      xaxis: {
        categories: [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ],
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      tooltip: { theme: 'light', shared: true },
      legend: { position: 'top' },
      grid: { borderColor: '#e0e0e0', strokeDashArray: 5 },
    };

    // 2. Démographie des employés
    this.demographicsChartOptions = {
      series: [],
      chart: {
        type: 'pie',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      labels: ['<25 ans', '25-35 ans', '36-45 ans', '>45 ans'],
      colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
      legend: {
        position: 'bottom',
        formatter: function (seriesName, opts) {
          return seriesName + ': ' + opts.w.globals.series[opts.seriesIndex];
        },
      },
      tooltip: {
        theme: 'light',
        y: { formatter: (val) => `${val} employés` },
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: { width: 200 },
            legend: { position: 'bottom' },
          },
        },
      ],
    };

    // 3. Compétences clés (Radar)
    this.skillsChartOptions = {
      series: [{ name: 'Compétences', data: [] }],
      chart: {
        type: 'radar',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      xaxis: { categories: [], labels: { show: true } },
      colors: ['#3B82F6'],
      markers: { size: 4 },
      tooltip: { theme: 'light' },
    };

    // 4. Satisfaction employés
    this.satisfactionChartOptions = {
      series: [
        { name: 'Insatisfaits', data: [] },
        { name: 'Neutres', data: [] },
        { name: 'Satisfaits', data: [] },
      ],
      chart: {
        type: 'bar',
        height: 350,
        stacked: true,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      colors: ['#DC2626', '#F59E0B', '#10B981'],
      xaxis: {
        categories: ['2020', '2021', '2022', '2023', '2024'],
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      tooltip: { theme: 'light' },
    };

    // 5. Distribution des salaires
    this.salaryDistributionChartOptions = {
      series: [{ name: 'Salaires', data: [] }],
      chart: {
        type: 'boxPlot',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      colors: ['#3B82F6', '#10B981', '#F59E0B'],
      tooltip: { theme: 'light', shared: false, intersect: true },
      plotOptions: {
        boxPlot: {
          colors: { upper: '#3B82F6', lower: '#10B981' },
        },
      },
      xaxis: {
        type: 'category',
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
    };

    // 6. Formation des employés
    this.trainingChartOptions = {
      series: [{ name: 'Heures de formation', data: [] }],
      chart: {
        type: 'area',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      colors: ['#8B5CF6'],
      xaxis: {
        categories: [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ],
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      tooltip: { theme: 'light' },
    };
  }

  private updateAllCharts(): void {
    this.updateMainChart();
    this.updateTurnoverChart();
    this.updateDemographicsChart();
    this.updateSkillsChart();
    this.updateSatisfactionChart();
    this.updateSalaryDistributionChart();
    this.updateTrainingChart();
  }

  private updateMainChart(): void {
    const currentYear = new Date().getFullYear();
    const monthlyData = Array(12)
      .fill(0)
      .map((_, month) => {
        return {
          newHires: this.employees.filter(
            (e) =>
              new Date(e.hireDate).getFullYear() === currentYear &&
              new Date(e.hireDate).getMonth() === month
          ).length,
          resignations: this.employees.filter((e) =>
            e.departures?.some(
              (d: { departureDate: string | number | Date }) =>
                new Date(d.departureDate).getFullYear() === currentYear &&
                new Date(d.departureDate).getMonth() === month
            )
          ).length,
          onLeave: this.employees.filter((e) =>
            e.leaves?.some(
              (l: {
                startDate: string | number | Date;
                endDate: string | number | Date;
              }) =>
                new Date(l.startDate).getFullYear() <= currentYear &&
                new Date(l.endDate).getFullYear() >= currentYear &&
                (new Date(l.startDate).getMonth() <= month ||
                  new Date(l.endDate).getMonth() >= month)
            )
          ).length,
        };
      });

    this.chartOptions.series = [
      { name: 'Nouveaux employés', data: monthlyData.map((m) => m.newHires) },
      {
        name: 'Employés démissionnaires',
        data: monthlyData.map((m) => m.resignations),
      },
      { name: 'Employés en congé', data: monthlyData.map((m) => m.onLeave) },
    ];
  }

  private updateTurnoverChart(): void {
    const currentYear = new Date().getFullYear();
    const monthlyData = Array(12)
      .fill(0)
      .map((_, month) => ({
        hires: this.employees.filter(
          (e) =>
            new Date(e.hireDate).getFullYear() === currentYear &&
            new Date(e.hireDate).getMonth() === month
        ).length,
        departures: this.employees.filter((e) =>
          e.departures?.some(
            (d: { departureDate: string | number | Date }) =>
              new Date(d.departureDate).getFullYear() === currentYear &&
              new Date(d.departureDate).getMonth() === month
          )
        ).length,
      }));

    this.turnoverChartOptions.series = [
      {
        name: 'Embauches',
        type: 'column',
        data: monthlyData.map((m) => m.hires),
      },
      {
        name: 'Départs',
        type: 'line',
        data: monthlyData.map((m) => m.departures),
      },
    ];
  }

  private updateDemographicsChart(): void {
    const ageGroups = [
      { name: '<25 ans', max: 25 },
      { name: '25-35 ans', min: 25, max: 35 },
      { name: '36-45 ans', min: 36, max: 45 },
      { name: '>45 ans', min: 46 },
    ];

    const currentDate = new Date();
    const counts = ageGroups.map((group) => {
      return this.employees.filter((employee) => {
        if (!employee.user?.profile?.birthDate) return false;
        const birthDate = new Date(employee.user.profile.birthDate);
        const age = currentDate.getFullYear() - birthDate.getFullYear();

        if (group.min && group.max) {
          return age >= group.min && age <= group.max;
        } else if (group.max) {
          return age < group.max;
        } else if (group.min) {
          return age >= group.min;
        }
        return false;
      }).length;
    });

    this.demographicsChartOptions.series = counts;
    this.demographicsChartOptions.labels = ageGroups.map((g) => g.name);
  }

  private updateSkillsChart(): void {
    const skillCounts: Record<string, number> = {};

    this.employees.forEach((employee) => {
      if (employee.position?.requiredSkills) {
        employee.position.requiredSkills.forEach((skill: string | number) => {
          skillCounts[skill] = (skillCounts[skill] || 0) + 1;
        });
      }
    });

    const topSkills = Object.entries(skillCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 6);

    this.skillsChartOptions.series = [
      {
        name: 'Compétences',
        data: topSkills.map((skill) => skill[1]),
      },
    ];
    this.skillsChartOptions.xaxis = {
      categories: topSkills.map((skill) => skill[0]),
    };
  }

  private updateSatisfactionChart(): void {
    const years = ['2020', '2021', '2022', '2023', '2024'];
    const satisfactionData = years.map((year) => {
      const evaluations = this.employees.flatMap(
        (e) =>
          e.performanceEvaluations?.filter(
            (pe: { periodEnd: string | number | Date }) =>
              new Date(pe.periodEnd).getFullYear().toString() === year
          ) || []
      );

      const total = evaluations.length;
      if (total === 0) return { satisfied: 0, neutral: 0, unsatisfied: 0 };

      const satisfied = evaluations.filter(
        (pe) =>
          pe.managerEvaluation?.includes('Excellent') ||
          pe.managerEvaluation?.includes('Good')
      ).length;

      const unsatisfied = evaluations.filter(
        (pe) =>
          pe.managerEvaluation?.includes('Poor') ||
          pe.managerEvaluation?.includes('Unsatisfactory')
      ).length;

      const neutral = total - satisfied - unsatisfied;

      return {
        satisfied: Math.round((satisfied / total) * 100),
        neutral: Math.round((neutral / total) * 100),
        unsatisfied: Math.round((unsatisfied / total) * 100),
      };
    });

    this.satisfactionChartOptions.series = [
      {
        name: 'Insatisfaits',
        data: satisfactionData.map((d) => d.unsatisfied),
      },
      { name: 'Neutres', data: satisfactionData.map((d) => d.neutral) },
      { name: 'Satisfaits', data: satisfactionData.map((d) => d.satisfied) },
    ];
  }

  private updateSalaryDistributionChart(): void {
    const positionGroups: Record<string, number[]> = {};

    this.employees.forEach((employee) => {
      if (employee.position && employee.salaries?.length > 0) {
        const position = employee.position.positionTitle;
        const latestSalary = employee.salaries.reduce(
          (
            prev: { effectiveDate: string | number | Date },
            current: { effectiveDate: string | number | Date }
          ) =>
            new Date(prev.effectiveDate) > new Date(current.effectiveDate)
              ? prev
              : current
        );
        const totalSalary =
          latestSalary.baseSalary +
          (latestSalary.housingAllowance || 0) +
          (latestSalary.transportAllowance || 0) +
          (latestSalary.bonus || 0);

        if (!positionGroups[position]) {
          positionGroups[position] = [];
        }
        positionGroups[position].push(totalSalary);
      }
    });

    const seriesData = Object.entries(positionGroups).map(
      ([position, salaries]) => {
        salaries.sort((a, b) => a - b);
        return {
          x: position,
          y: [
            salaries[0],
            salaries[Math.floor(salaries.length * 0.25)],
            salaries[Math.floor(salaries.length * 0.5)],
            salaries[Math.floor(salaries.length * 0.75)],
            salaries[salaries.length - 1],
          ],
        };
      }
    );

    this.salaryDistributionChartOptions.series = [
      {
        name: 'Salaires',
        data: seriesData,
      },
    ];
  }

  private updateTrainingChart(): void {
    const currentYear = new Date().getFullYear();
    const monthlyHours = Array(12)
      .fill(0)
      .map((_, month) => {
        return this.employees.reduce((total, employee) => {
          return (
            total +
            (employee.trainings
              ?.filter(
                (t: { startDate: string | number | Date }) =>
                  new Date(t.startDate).getFullYear() === currentYear &&
                  new Date(t.startDate).getMonth() === month
              )
              .reduce(
                (
                  sum: number,
                  training: {
                    endDate: string | number | Date;
                    startDate: string | number | Date;
                  }
                ) => {
                  const hours =
                    (new Date(training.endDate).getTime() -
                      new Date(training.startDate).getTime()) /
                    (1000 * 60 * 60);
                  return sum + hours;
                },
                0
              ) || 0)
          );
        }, 0);
      });

    this.trainingChartOptions.series = [
      {
        name: 'Heures de formation',
        data: monthlyHours,
      },
    ];
  }

  private setDummyData(): void {
    // Main chart
    this.chartOptions.series = [
      {
        name: 'Nouveaux employés',
        data: [12, 15, 10, 18, 20, 15, 22, 25, 20, 18, 15, 10],
      },
      {
        name: 'Employés démissionnaires',
        data: [5, 3, 8, 6, 7, 4, 6, 5, 4, 6, 8, 7],
      },
      { name: 'Employés en congé', data: [3, 4, 5, 6, 4, 7, 5, 6, 8, 7, 6, 5] },
    ];

    // Turnover chart
    this.turnoverChartOptions.series = [
      {
        name: 'Embauches',
        type: 'column',
        data: [12, 15, 8, 10, 5, 7, 9, 11, 13, 10, 8, 6],
      },
      {
        name: 'Départs',
        type: 'line',
        data: [5, 8, 3, 6, 2, 4, 7, 5, 6, 4, 5, 7],
      },
    ];

    // Demographics chart
    this.demographicsChartOptions.series = [44, 55, 13, 43];
    this.demographicsChartOptions.labels = [
      '<25 ans',
      '25-35 ans',
      '36-45 ans',
      '>45 ans',
    ];

    // Skills chart
    this.skillsChartOptions.series = [
      { name: 'Compétences', data: [80, 50, 30, 60, 75, 40] },
    ];
    this.skillsChartOptions.xaxis = {
      categories: [
        'Tech',
        'Gestion',
        'Communication',
        'Leadership',
        'Créativité',
        'Résolution problèmes',
      ],
    };

    // Satisfaction chart
    this.satisfactionChartOptions.series = [
      { name: 'Insatisfaits', data: [10, 5, 8, 12, 7] },
      { name: 'Neutres', data: [20, 15, 12, 18, 25] },
      { name: 'Satisfaits', data: [70, 80, 80, 70, 68] },
    ];

    // Salary distribution chart
    this.salaryDistributionChartOptions.series = [
      {
        name: 'Salaires',
        data: [
          { x: 'Développeurs', y: [40000, 50000, 60000, 70000, 90000] },
          { x: 'Managers', y: [50000, 60000, 75000, 85000, 100000] },
          { x: 'RH', y: [35000, 45000, 55000, 65000, 75000] },
        ],
      },
    ];

    // Training chart
    this.trainingChartOptions.series = [
      {
        name: 'Heures de formation',
        data: [120, 90, 150, 200, 180, 210, 170, 190, 220, 240, 210, 230],
      },
    ];
  }

  private updateChartThemes(theme: string): void {
    const charts = [
      this.chartOptions,
      this.turnoverChartOptions,
      this.demographicsChartOptions,
      this.skillsChartOptions,
      this.satisfactionChartOptions,
      this.salaryDistributionChartOptions,
      this.trainingChartOptions,
    ];

    charts.forEach((chart) => {
      if (chart) {
        chart.tooltip = { ...chart.tooltip, theme };
        chart.grid = {
          borderColor: theme === 'dark' ? '#2d3748' : '#e0e0e0',
          strokeDashArray: 5,
          padding: { left: 0, right: 0 },
        };
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
