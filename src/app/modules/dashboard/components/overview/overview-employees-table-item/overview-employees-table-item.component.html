<td class="px-4 py-3 text-gray-800 text-xs dark:text-gray-200">
  {{ employee.id | slice : 0 : 13 }}
</td>
<td class="px-4 py-3">
  <div *ngIf="employee.user.profile?.avatar; else defaultAvatar">
    <img
      [src]="employee.user.profile?.avatar"
      alt="Avatar"
      class="w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
    />
  </div>
  <ng-template #defaultAvatar>
    <div
      class="w-10 h-10 flex items-center justify-center rounded-full text-white font-medium bg-gradient-to-r from-blue-500 to-indigo-600"
    >
      {{ (employee.user?.profile?.firstName)![0] | uppercase }}
    </div>
  </ng-template>
</td>
<td class="px-4 py-3 font-medium text-gray-800 dark:text-white">
  {{ employee.user.profile?.firstName }} {{ employee.user.profile?.lastName }}
</td>
<td class="px-4 py-3 text-gray-600 text-xs dark:text-gray-300">
  {{ employee.department?.departmentName }}
</td>
<td class="px-4 py-3 text-gray-600 text-xs dark:text-gray-300">
  {{ employee.position?.positionTitle }}
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ employee.user.email }}
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ employee.user.profile?.phoneNumber || "N/A" }}
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ employee.hireDate | date : "shortDate" }}
</td>
