import { Component, Input, OnInit } from '@angular/core';
import { EmployeeData } from '../../../models/employee';
import { DatePipe, NgIf, SlicePipe, UpperCasePipe } from '@angular/common';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
  selector: '[overview-employees-table-item]',
  templateUrl: './overview-employees-table-item.component.html',
  imports: [AngularSvgIconModule, UpperCasePipe, DatePipe, NgIf, SlicePipe],
})
export class OverviewEmployeesTableItemComponent implements OnInit {
  @Input() employee = <EmployeeData>{};

  constructor() {}

  ngOnInit(): void {}
}
