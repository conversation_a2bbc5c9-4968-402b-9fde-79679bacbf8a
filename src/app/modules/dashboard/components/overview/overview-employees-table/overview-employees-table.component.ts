import { Component, OnInit } from '@angular/core';
import { EmployeeData } from '../../../models/employee';
import { Ng<PERSON><PERSON>, Ng<PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { OverviewEmployeesTableItemComponent } from '../overview-employees-table-item/overview-employees-table-item.component';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { EmployeeCreateModalComponent } from '../../../pages/employees/components/employee-create-modal/employee-create-modal.component';
import { Router } from '@angular/router';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { Company } from 'src/app/core/models/company.model';

@Component({
  selector: '[overview-employees-table]',
  templateUrl: './overview-employees-table.component.html',
  imports: [
    Ng<PERSON><PERSON>,
    NgIf,
    EmployeeCreateModalComponent,
    OverviewEmployeesTableItemComponent,
    NgClass,
  ],
})
export class OverviewEmployeesTableComponent implements OnInit {
  employees: EmployeeData[] = []; // Stocker les employés
  showCreateModal = false;
  selectedEmployee: EmployeeData | null = null;
  isLoading = true;
  company!: Company;

  constructor(
    private employeeService: EmployeeService,
    private router: Router,
    private store: Store<AppState>
  ) {} // Injecter le service

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
    });
    this.getEmployees(); // Appeler la méthode pour récupérer les employés lors de l'initialisation du composant
  }

  getEmployees(): void {
    this.employeeService.getEmployees(this.company.id!).subscribe(
      (data) => {
        this.employees = data; // Stocker les employés récupérés dans la variable employees
        this.isLoading = false;
      },
      (error) => {
        this.isLoading = false;
        console.error('Error fetching employees:', error); // Gérer les erreurs
      }
    );
  }

  openCreateModal(): void {
    this.showCreateModal = true;
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
  }

  addEmployee(employeeData: any): void {
    this.employeeService
      .createEmployee(this.company.id!, employeeData)
      .subscribe(
        (response) => {
          if (response) {
            this.getEmployees();
            this.closeCreateModal();
          }
        },
        (error) => {
          console.error("Erreur lors de l'ajout de l'employé:", error);
        }
      );
  }
  selectEmployee(employee: EmployeeData): void {
    this.selectedEmployee =
      this.selectedEmployee === employee ? null : employee;
  }

  clearSelection(): void {
    this.selectedEmployee = null;
  }

  editEmployee(employee: EmployeeData): void {
    if (employee && employee.id) {
      this.router.navigate(['/dashboard/employees', employee.id, 'profile']);
    }
  }

  deleteEmployee(employee: EmployeeData): void {
    if (
      confirm(
        `Voulez-vous vraiment supprimer ${employee.user?.profile?.firstName} ${employee?.user?.profile?.lastName} ?`
      )
    ) {
      this.employeeService
        .getEmployeeById(this.company.id!, employee.id)
        .subscribe(() => {
          this.getEmployees(); // Recharger la liste après suppression
          this.clearSelection(); // Désélectionner l'employé
        });
    }
  }
}
