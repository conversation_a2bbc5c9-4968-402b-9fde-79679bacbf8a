import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { CompanyService } from 'src/app/core/services/company/company.service';
import {
  CurrencyPipe,
  DatePipe,
  JsonPipe,
  NgClass,
  NgFor,
  NgIf,
  NgSwitch,
  NgSwitchCase,
} from '@angular/common';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { TimesheetListComponent } from '../../timesheets/timesheet-list.component';
import { PayslipService } from 'src/app/core/services/payslip/payslip.service';
import { EmployeeData } from '../../../models/employee';
import { SalaryService } from 'src/app/core/services/salary/salary.service';
import { SalaryListComponent } from '../../salaries/salary-list.component';
import { PayslipListComponent } from '../../payslips/payslip-list.component';
import { AppState } from 'src/app/core/store/app.state';
import { select, Store } from '@ngrx/store';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { Department, Position } from 'src/app/core/models/company.model';
import { DepartmentService } from 'src/app/core/services/department/department.service';
import { PositionService } from 'src/app/core/services/department/position.service';

@Component({
  selector: 'app-employee-profile',
  templateUrl: './employee-profile.component.html',
  imports: [
    NgClass,
    NgSwitch,
    NgSwitchCase,
    FormsModule,
    ReactiveFormsModule,
    NgIf,
    NgFor,
    DatePipe,
    TimesheetListComponent,
    SalaryListComponent,
    PayslipListComponent,
  ],
  animations: [
    trigger('fadeAnimation', [
      state('void', style({ opacity: 0 })),
      transition(':enter, :leave', [animate('300ms ease-in-out')]),
    ]),
  ],
})
export class EmployeeProfileComponent implements OnInit {
  hasAdminRights: boolean = false; // Changé de 'any' à boolean pour plus de clarté

  employee!: EmployeeData;
  departments: Department[] = [];
  positions: Position[] = [];
  isEditMode = false;
  editForm!: FormGroup;
  recentActivities: any[] = [];
  currentCompanyId!: string;

  // Configuration des onglets
  tabs = [
    { id: 'personal', name: 'Informations' },
    { id: 'salary', name: 'Salaires' },

    { id: 'payslips', name: 'Fiches de paie' },
    { id: 'leave', name: 'Congés' },
    { id: 'evaluations', name: 'Évaluations' },
    { id: 'trainings', name: 'Formations' },
    { id: 'timesheets', name: 'Feuilles de temps' },
    { id: 'activity', name: 'Activités récentes' },
  ];
  activeTab = 'personal';

  constructor(
    private employeeService: EmployeeService,
    private store: Store<AppState>,
    private route: ActivatedRoute,
    private departmentService: DepartmentService,
    private positionService: PositionService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.currentCompanyId = company.id!;
    });
    this.route.params.subscribe((params) => {
      const employeeId = params['id'];
      this.loadEmployeeData(employeeId);
    });
  }

  loadEmployeeData(employeeId: string): void {
    this.employeeService
      .getEmployeeById(this.currentCompanyId, employeeId)
      .subscribe(
        (data) => {
          this.employee = data;
          this.loadDepartments();
        },
        (error) => {}
      );
  }

  setupDepartmentChangeListener(): void {
    this.editForm
      .get('departmentId')
      ?.valueChanges.subscribe((departmentId) => {
        if (departmentId) {
          console.log('Department changed to:', departmentId); // Debug
          this.loadPositions(departmentId);
          this.editForm.get('positionId')?.reset();
        }
      });
  }

  loadRecentActivities(employeeId: number): void {}

  initializeEditForm(): void {
    this.editForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: [
        this.employee.user.email,
        [Validators.required, Validators.email],
      ],
      birthDate: [this.employee.user?.profile?.birthDate, Validators.required],
      hireDate: [
        this.formatDateForInput(this.employee.hireDate),
        Validators.required,
      ],
      departmentId: [this.employee.department?.id, Validators.required],
      positionId: [this.employee.position?.id, Validators.required],
      phone: [this.employee.user?.profile?.phoneNumber, Validators.required],
    });
    this.setupDepartmentChangeListener();
  }

  formatDateForInput(dateString: string): string {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  }

  toggleEditMode(): void {
    this.isEditMode = !this.isEditMode;
    if (!this.isEditMode) {
      this.initializeEditForm();
    }
  }

  saveProfile(): void {
    const updatedEmployee = {
      ...this.employee,
      hireDate: this.editForm.value.hireDate,
      positionId: this.editForm.value.positionId,
      departmentId: this.editForm.value.departmentId,
      user: {
        ...this.employee.user,
        email: this.editForm.value.email,
        profile: {
          phoneNumber: this.editForm.value.phone,
        },
      },
    };
    this.employeeService
      .updateEmployee(this.currentCompanyId, this.employee.id, updatedEmployee)
      .subscribe({
        next: (employee) => {
          this.loadEmployeeData(employee.id);
        },
        error: (error) => {
          console.log(error);
        },
      });
  }

  loadDepartments(): void {
    this.departmentService.getAllDepartments(this.currentCompanyId).subscribe({
      next: (departments) => {
        this.departments = departments;
        this.initializeEditForm();
        if (this.employee.department?.id) {
          this.loadPositions(this.employee.department.id);
        }
      },
      error: () => {},
    });
  }

  loadPositions(departmentId: string): void {
    this.positionService
      .getAllPositions(this.currentCompanyId, departmentId)
      .subscribe({
        next: (positions) => {
          this.positions = positions;
        },
        error: () => {},
      });
  }

  getSortedLeaves(): any[] {
    if (!this.employee.leaves) {
      return [];
    }
    return [...this.employee.leaves].sort(
      (a, b) =>
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    );
  }

  getSortedEvaluations(): any[] {
    if (!this.employee.performanceEvaluations) {
      return [];
    }
    return [...this.employee.performanceEvaluations].sort(
      (a, b) =>
        new Date(b.evaluationDate).getTime() -
        new Date(a.evaluationDate).getTime()
    );
  }

  getSortedTrainings(): any[] {
    if (!this.employee.trainings) {
      return [];
    }
    return [...this.employee.trainings].sort(
      (a, b) =>
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    );
  }

  getSortedTimesheets(): any[] {
    if (!this.employee.timesheets) {
      return [];
    }
    return [...this.employee.timesheets].sort(
      (a, b) =>
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    );
  }

  calculateRemainingLeaves(): number {
    if (!this.employee.leaves) {
      return 0;
    }

    const currentYear = new Date().getFullYear();
    const currentDate = new Date();

    // Vérifier si l'employé a un contrat valide
    if (!this.employee.contract || !this.employee.contract.startDate) {
      return 0;
    }

    // Calculer le nombre de jours de congés alloués selon l'ancienneté
    const contractStartDate = new Date(this.employee.contract.startDate);
    const yearsOfService = currentYear - contractStartDate.getFullYear();
    let allocatedLeaves = 25; // Base de 25 jours

    // Ajuster selon l'ancienneté
    if (yearsOfService >= 10) {
      allocatedLeaves = 35;
    } else if (yearsOfService >= 5) {
      allocatedLeaves = 30;
    }

    // Calculer les congés utilisés pour l'année en cours
    const usedLeaves = this.employee.leaves
      .filter((leave) => {
        const startDate = new Date(leave.startDate);
        const endDate = new Date(leave.endDate);

        // Vérifier si le congé est approuvé et dans l'année en cours
        return (
          leave.status === 'APPROVED' &&
          startDate.getFullYear() === currentYear &&
          endDate.getFullYear() === currentYear
        );
      })
      .reduce((total, leave) => {
        // Calculer la durée exacte du congé en jours
        const startDate = new Date(leave.startDate);
        const endDate = new Date(leave.endDate);
        const duration = this.calculateLeaveDuration(leave);

        // Vérifier si le congé est valide
        if (isNaN(duration) || duration < 0) {
          return total;
        }

        return total + duration;
      }, 0);

    // Calculer les congés restants
    const remainingLeaves = allocatedLeaves - usedLeaves;

    // S'assurer que le résultat n'est pas négatif
    return Math.max(0, remainingLeaves);
  }

  getSortedPayslips() {
    if (!this.employee.payslips) return [];
    return [...this.employee.payslips].sort((a, b) => {
      // Tri par année décroissante puis par mois décroissant
      if (a.year !== b.year) return b.year - a.year;
      return b.month - a.month;
    });
  }

  calculateLeaveDuration(leave: any): number {
    const start = new Date(leave.startDate);
    const end = new Date(leave.endDate);
    return (
      Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1
    );
  }

  getTimesheetStatusClass(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getLeaveStatusClass(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getActivityIcon(type: string): string {
    switch (type.toUpperCase()) {
      case 'TIMESHEETS':
        return '⏱️';
      case 'LEAVE':
        return '🏖️';
      case 'EVALUATIONS':
        return '📊';
      case 'TRAININGS':
        return '🎓';
      case 'SALARY':
        return '💰';
      case 'BENEFIT':
        return '🎁';
      default:
        return '📝';
    }
  }

  generatePassword(): void {
    if (!this.employee?.user?.id) return;

    this.employeeService
      .generateAndSendPassword(this.employee.user.id)
      .subscribe({
        next: (response) => {
          // Affichez un message de succès ou effectuez d'autres actions
          console.log('Password generated and sent:', response.message);
          // Vous pourriez ajouter une notification ici
        },
        error: (error) => {
          console.error('Failed to generate password:', error);
          // Affichez un message d'erreur
        },
      });
  }
}
