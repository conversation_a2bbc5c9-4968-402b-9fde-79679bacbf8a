import { Routes } from '@angular/router';

export const REPORTS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./report-dashboard/report-dashboard.component').then(m => m.ReportDashboardComponent),
    title: 'Tableau de Bord Rapports - LuminaHR'
  },
  {
    path: 'hr',
    loadComponent: () => import('./hr-reports/hr-reports.component').then(m => m.HrReportsComponent),
    title: 'Rapports RH - LuminaHR'
  },
  {
    path: 'analytics',
    loadComponent: () => import('./analytics/analytics.component').then(m => m.AnalyticsComponent),
    title: 'Analytics RH - LuminaHR'
  },
  {
    path: 'create',
    loadComponent: () => import('./report-create/report-create.component').then(m => m.ReportCreateComponent),
    title: 'Créer un Rapport - LuminaHR'
  },
  {
    path: 'view/:id',
    loadComponent: () => import('./report-view/report-view.component').then(m => m.ReportViewComponent),
    title: 'Détails du Rapport - LuminaHR'
  }
];
