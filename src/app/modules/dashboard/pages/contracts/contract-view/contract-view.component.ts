import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { ContractService } from '../../../../../core/services/contract/contract.service';
import { 
  Contract, 
  ContractType, 
  ContractStatus 
} from '../../../../../core/models/contract.model';

@Component({
  selector: 'app-contract-view',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './contract-view.component.html'
})
export class ContractViewComponent implements OnInit, OnDestroy {
  contract: Contract | null = null;
  contractId: string;
  
  private subscriptions = new Subscription();

  constructor(
    private contractService: ContractService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.contractId = this.route.snapshot.paramMap.get('id') || '';
  }

  ngOnInit(): void {
    if (this.contractId) {
      this.loadContract();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadContract(): void {
    this.subscriptions.add(
      this.contractService.getContractById(this.contractId).subscribe(contract => {
        this.contract = contract;
      })
    );
  }

  // Navigation
  goBack(): void {
    this.router.navigate(['/dashboard/contracts']);
  }

  editContract(): void {
    this.router.navigate(['/dashboard/contracts/edit', this.contractId]);
  }

  viewEmployeeProfile(): void {
    if (this.contract?.employeeId) {
      this.router.navigate(['/dashboard/employees', this.contract.employeeId, 'profile']);
    }
  }

  // Actions
  duplicateContract(): void {
    if (this.contract) {
      this.subscriptions.add(
        this.contractService.duplicateContract(this.contract.id).subscribe(() => {
          this.router.navigate(['/dashboard/contracts']);
        })
      );
    }
  }

  downloadPDF(): void {
    // TODO: Implémenter la génération PDF
    alert('Fonctionnalité de téléchargement PDF à implémenter');
  }

  sendForSignature(): void {
    if (this.contract && this.contract.status !== ContractStatus.SIGNED) {
      // TODO: Implémenter l'envoi pour signature
      alert('Fonctionnalité d\'envoi pour signature à implémenter');
    }
  }

  // Méthodes utilitaires
  getEmployeeName(): string {
    if (this.contract?.employee?.user?.profile) {
      const profile = this.contract.employee.user.profile;
      return `${profile.firstName} ${profile.lastName}`;
    }
    return `Employé ${this.contract?.employeeId}`;
  }

  getEmployeeInitials(): string {
    if (this.contract?.employee?.user?.profile) {
      const profile = this.contract.employee.user.profile;
      return `${profile.firstName?.[0] || ''}${profile.lastName?.[0] || ''}`.toUpperCase();
    }
    return 'EMP';
  }

  getTypeLabel(type: ContractType): string {
    const labels: { [key in ContractType]: string } = {
      [ContractType.CDI]: 'CDI',
      [ContractType.CDD]: 'CDD',
      [ContractType.STAGE]: 'Stage',
      [ContractType.FREELANCE]: 'Freelance',
      [ContractType.APPRENTISSAGE]: 'Apprentissage',
      [ContractType.INTERIM]: 'Intérim',
      [ContractType.CONSULTANT]: 'Consultant'
    };
    return labels[type];
  }

  getStatusLabel(status: ContractStatus): string {
    const labels: { [key in ContractStatus]: string } = {
      [ContractStatus.DRAFT]: 'Brouillon',
      [ContractStatus.PENDING_SIGNATURE]: 'En attente de signature',
      [ContractStatus.SIGNED]: 'Signé',
      [ContractStatus.ACTIVE]: 'Actif',
      [ContractStatus.EXPIRED]: 'Expiré',
      [ContractStatus.TERMINATED]: 'Résilié',
      [ContractStatus.CANCELLED]: 'Annulé'
    };
    return labels[status];
  }

  getStatusClasses(status: ContractStatus): string {
    const classes: { [key in ContractStatus]: string } = {
      [ContractStatus.DRAFT]: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [ContractStatus.PENDING_SIGNATURE]: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [ContractStatus.SIGNED]: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [ContractStatus.ACTIVE]: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [ContractStatus.EXPIRED]: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [ContractStatus.TERMINATED]: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [ContractStatus.CANCELLED]: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
    };
    return classes[status];
  }

  getProcessedContent(): string {
    if (!this.contract) return '';
    
    let content = this.contract.content;
    
    // Remplacer les variables par leurs valeurs
    this.contract.variables.forEach(variable => {
      const placeholder = `[${variable.key}]`;
      content = content.replace(new RegExp(placeholder, 'g'), variable.value || '');
    });
    
    // Remplacer les variables d'employé communes
    if (this.contract.employee?.user?.profile) {
      const profile = this.contract.employee.user.profile;
      content = content.replace(/\[EMPLOYEE_NAME\]/g, `${profile.firstName} ${profile.lastName}`);
      content = content.replace(/\[EMPLOYEE_FIRST_NAME\]/g, profile.firstName || '');
      content = content.replace(/\[EMPLOYEE_LAST_NAME\]/g, profile.lastName || '');
      content = content.replace(/\[EMPLOYEE_EMAIL\]/g, this.contract.employee.user.email || '');
    }
    
    // Remplacer les variables de contrat communes
    content = content.replace(/\[CONTRACT_TYPE\]/g, this.getTypeLabel(this.contract.contractType));
    content = content.replace(/\[START_DATE\]/g, new Date(this.contract.startDate).toLocaleDateString('fr-FR'));
    content = content.replace(/\[END_DATE\]/g, this.contract.endDate ? new Date(this.contract.endDate).toLocaleDateString('fr-FR') : 'Indéterminée');
    content = content.replace(/\[SALARY\]/g, this.contract.salary.toLocaleString('fr-FR') + ' €');
    content = content.replace(/\[WORKING_HOURS\]/g, this.contract.workingHours.toString());
    
    // Variables d'entreprise (à adapter selon vos données)
    content = content.replace(/\[COMPANY_NAME\]/g, 'LuminaHR');
    content = content.replace(/\[COMPANY_ADDRESS\]/g, '123 Rue de la Technologie, 75001 Paris');
    
    return content;
  }
}
