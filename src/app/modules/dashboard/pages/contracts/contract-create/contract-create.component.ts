import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormsModule,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { ContractService } from '../../../../../core/services/contract/contract.service';
import { EmployeeService } from '../../../../../core/services/employee/employee.service';
import {
  ContractType,
  ContractTemplate,
  TemplateVariable,
  VariableType,
  ContractFormData,
  ContractVariable,
} from '../../../../../core/models/contract.model';
import { Employee } from '../../../../../core/models/company.model';

@Component({
  selector: 'app-contract-create',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './contract-create.component.html',
})
export class ContractCreateComponent implements OnInit, OnDestroy {
  contractForm: FormGroup;
  isEditing = false;
  isSubmitting = false;
  contractId?: string;

  // Données pour les selects
  employees: Employee[] = [];
  templates: ContractTemplate[] = [];
  contractTypes = Object.values(ContractType);

  // Variables du template sélectionné
  templateVariables: TemplateVariable[] = [];
  variableValues: { [key: string]: any } = {};

  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private contractService: ContractService,
    private employeeService: EmployeeService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.contractForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadEmployees();
    this.loadTemplates();
    this.checkEditMode();
    this.handleTemplateFromQuery();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      employeeId: ['', Validators.required],
      templateId: [''],
      contractType: ['', Validators.required],
      title: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: [''],
      salary: ['', [Validators.required, Validators.min(1)]],
      workingHours: [
        35,
        [Validators.required, Validators.min(1), Validators.max(60)],
      ],
      notes: [''],
    });
  }

  private loadEmployees(): void {
    // TODO: Récupérer le companyId depuis le store ou le service d'auth
    const companyId = 'current-company-id'; // Placeholder
    this.subscriptions.add(
      this.employeeService.getEmployees(companyId).subscribe((employees) => {
        this.employees = employees;
      })
    );
  }

  private loadTemplates(): void {
    this.subscriptions.add(
      this.contractService.getTemplates().subscribe((templates) => {
        this.templates = templates.filter((t) => t.isActive);
      })
    );
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditing = true;
      this.contractId = id;
      this.loadContractForEdit(id);
    }
  }

  private handleTemplateFromQuery(): void {
    const templateId = this.route.snapshot.queryParamMap.get('templateId');
    if (templateId) {
      this.contractForm.patchValue({ templateId });
      this.onTemplateChange();
    }
  }

  private loadContractForEdit(id: string): void {
    this.subscriptions.add(
      this.contractService.getContractById(id).subscribe((contract) => {
        if (contract) {
          this.contractForm.patchValue({
            employeeId: contract.employeeId,
            templateId: contract.templateId || '',
            contractType: contract.contractType,
            title: contract.title,
            startDate: this.formatDateForInput(contract.startDate),
            endDate: contract.endDate
              ? this.formatDateForInput(contract.endDate)
              : '',
            salary: contract.salary,
            workingHours: contract.workingHours,
            notes: contract.notes || '',
          });

          // Charger les variables existantes
          contract.variables.forEach((variable) => {
            this.variableValues[variable.key] = variable.value;
          });

          if (contract.templateId) {
            this.onTemplateChange();
          }
        }
      })
    );
  }

  onTemplateChange(): void {
    const templateId = this.contractForm.get('templateId')?.value;
    if (templateId) {
      const template = this.templates.find((t) => t.id === templateId);
      if (template) {
        this.templateVariables = template.variables;

        // Pré-remplir le type de contrat si défini dans le template
        if (template.contractType) {
          this.contractForm.patchValue({ contractType: template.contractType });
        }

        // Initialiser les valeurs par défaut des variables
        template.variables.forEach((variable) => {
          if (variable.defaultValue && !this.variableValues[variable.key]) {
            this.variableValues[variable.key] = variable.defaultValue;
          }
        });
      }
    } else {
      this.templateVariables = [];
      this.variableValues = {};
    }
  }

  onSubmit(): void {
    if (this.contractForm.valid) {
      this.isSubmitting = true;

      const formData = this.prepareFormData();

      const operation = this.isEditing
        ? this.contractService.updateContract(this.contractId!, formData)
        : this.contractService.createContract(formData);

      this.subscriptions.add(
        operation.subscribe({
          next: () => {
            this.router.navigate(['/dashboard/contracts']);
          },
          error: (error) => {
            console.error('Erreur lors de la sauvegarde:', error);
            this.isSubmitting = false;
          },
        })
      );
    }
  }

  saveDraft(): void {
    if (
      this.contractForm.get('employeeId')?.value &&
      this.contractForm.get('title')?.value
    ) {
      this.isSubmitting = true;

      const formData = this.prepareFormData();

      const operation = this.isEditing
        ? this.contractService.updateContract(this.contractId!, {
            ...formData,
            status: 'DRAFT' as any,
          })
        : this.contractService.createContract(formData);

      this.subscriptions.add(
        operation.subscribe({
          next: () => {
            this.router.navigate(['/dashboard/contracts']);
          },
          error: (error) => {
            console.error('Erreur lors de la sauvegarde:', error);
            this.isSubmitting = false;
          },
        })
      );
    }
  }

  private prepareFormData(): ContractFormData {
    const formValue = this.contractForm.value;

    // Préparer les variables du contrat
    const variables: ContractVariable[] = this.templateVariables.map(
      (templateVar) => ({
        key: templateVar.key,
        value: this.variableValues[templateVar.key] || '',
        type: templateVar.type,
        label: templateVar.label,
        required: templateVar.required,
      })
    );

    return {
      employeeId: formValue.employeeId,
      templateId: formValue.templateId || undefined,
      contractType: formValue.contractType,
      title: formValue.title,
      startDate: new Date(formValue.startDate),
      endDate: formValue.endDate ? new Date(formValue.endDate) : undefined,
      salary: formValue.salary,
      currency: 'EUR',
      workingHours: formValue.workingHours,
      variables,
      notes: formValue.notes || undefined,
    };
  }

  goBack(): void {
    this.router.navigate(['/dashboard/contracts']);
  }

  // Méthodes utilitaires
  getTypeLabel(type: ContractType): string {
    const labels: { [key in ContractType]: string } = {
      [ContractType.CDI]: 'CDI',
      [ContractType.CDD]: 'CDD',
      [ContractType.STAGE]: 'Stage',
      [ContractType.FREELANCE]: 'Freelance',
      [ContractType.APPRENTISSAGE]: 'Apprentissage',
      [ContractType.INTERIM]: 'Intérim',
      [ContractType.CONSULTANT]: 'Consultant',
    };
    return labels[type];
  }

  getInputType(variableType: VariableType): string {
    switch (variableType) {
      case VariableType.EMAIL:
        return 'email';
      case VariableType.PHONE:
        return 'tel';
      default:
        return 'text';
    }
  }

  private formatDateForInput(date: Date): string {
    return new Date(date).toISOString().split('T')[0];
  }
}
