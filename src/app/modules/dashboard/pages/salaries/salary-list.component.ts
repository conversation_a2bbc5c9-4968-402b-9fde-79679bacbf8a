import { Component, Input, OnInit } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { EmployeeData } from 'src/app/modules/dashboard/models/employee';
import { CurrencyPipe, DatePipe, NgClass, NgFor, NgIf } from '@angular/common';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { Company } from 'src/app/core/models/company.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import {
  CreateSalaryDto,
  Salary,
  SalaryService,
  UpdateSalaryDto,
} from 'src/app/core/services/salary/salary.service';
import { SalaryModalComponent } from './components/salary-create-modal/salary-create-modal.component';
import { Observable, map } from 'rxjs';

@Component({
  selector: 'app-salary-list',
  templateUrl: './salary-list.component.html',
  standalone: true,
  imports: [
    NgClass,
    CurrencyPipe,
    SalaryModalComponent,
    NgFor,
    NgIf,
    DatePipe,
    FormsModule,
    ReactiveFormsModule,
  ],
})
export class SalaryListComponent implements OnInit {
  @Input() employeeId?: string;

  salaries: Salary[] = [];
  employees: EmployeeData[] = [];
  selectedSalary: Salary | null = null;
  filterForm: FormGroup;
  showModal = false;
  lastUpdateMinutes = 0;
  isLoading = false;
  isEditing = false;
  company?: Company;

  constructor(
    private salaryService: SalaryService,
    private employeeService: EmployeeService,
    private store: Store<AppState>,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      employeeId: [''],
      startDate: [''],
      endDate: [''],
    });

    this.lastUpdateMinutes = Math.floor(Math.random() * 60);
    setInterval(() => this.lastUpdateMinutes++, 60000);
  }

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
      this.loadEmployees();
      this.loadAllSalaries();
    });
  }

  loadEmployees(): void {
    if (this.company?.id) {
      this.employeeService.getEmployees(this.company.id).subscribe({
        next: (data) => (this.employees = data),
        error: (error) => console.error('Error loading employees:', error),
      });
    }
  }

  loadAllSalaries(): void {
    this.isLoading = true;

    const employeeId =
      this.employeeId || this.filterForm.get('employeeId')?.value;
    const companyId = this.company?.id;

    if (!companyId) {
      this.isLoading = false;
      return;
    }

    const salaryObservable: Observable<Salary[]> = employeeId
      ? this.salaryService.getAllSalariesForEmployee(employeeId)
      : this.salaryService.getAllSalariesForCompany(companyId);

    salaryObservable.subscribe({
      next: (data) => {
        this.salaries = this.filterSalaries(data);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading salaries:', error);
        this.isLoading = false;
      },
    });
  }

  filterSalaries(salaries: Salary[]): Salary[] {
    const startDate = this.filterForm.get('startDate')?.value;
    const endDate = this.filterForm.get('endDate')?.value;

    if (!startDate && !endDate) return salaries;

    return salaries.filter((salary) => {
      const effectiveDate = new Date(salary.effectiveDate);
      const start = startDate ? new Date(startDate) : null;
      const end = endDate ? new Date(endDate) : null;

      return (
        (!start || effectiveDate >= start) && (!end || effectiveDate <= end)
      );
    });
  }

  selectSalary(salary: Salary): void {
    this.selectedSalary = salary;
  }

  applyFilters(): void {
    this.loadAllSalaries();
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadAllSalaries();
  }

  openModal(isEditing: boolean): void {
    this.isEditing = isEditing;
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedSalary = null;
  }

  saveSalary(formData: CreateSalaryDto | UpdateSalaryDto): void {
    const operation = this.selectedSalary
      ? this.salaryService.updateSalary(
          this.selectedSalary.id,
          formData as UpdateSalaryDto
        )
      : this.salaryService.createSalary(formData as CreateSalaryDto);

    operation.subscribe({
      next: (salary) => {
        if (this.selectedSalary) {
          this.salaries = this.salaries.map((s) =>
            s.id === salary.id ? salary : s
          );
        } else {
          this.salaries = [salary, ...this.salaries];
        }
        this.closeModal();
        this.lastUpdateMinutes = 0;
      },
      error: (error) => console.error('Error saving salary:', error),
    });
  }

  deleteSalary(): void {
    if (!this.selectedSalary) return;

    if (confirm(`Delete salary (ID: ${this.selectedSalary.id})?`)) {
      this.salaryService.deleteSalary(this.selectedSalary.id).subscribe({
        next: () => {
          this.salaries = this.salaries.filter(
            (s) => s.id !== this.selectedSalary?.id
          );
          this.selectedSalary = null;
          this.lastUpdateMinutes = 0;
        },
        error: (error) => console.error('Error deleting salary:', error),
      });
    }
  }

  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    return employee
      ? `${employee.user?.profile?.firstName} ${employee.user?.profile?.lastName}`
      : `Employee #${employeeId}`;
  }

  calculateTotalSalary(salary: Salary): number {
    return (
      salary.baseSalary +
      (salary.housingAllowance || 0) +
      (salary.transportAllowance || 0) +
      (salary.bonus || 0) +
      (salary.overtimeHours || 0) * (salary.overtimeRate || 0)
    );
  }
}
