<div class="flex flex-col rounded-xl bg-white px-6 py-6 dark:bg-gray-800">
  <!-- Header avec titre et bouton d'ajout -->
  <div class="mb-6 flex items-center justify-between">
    <div class="flex items-center gap-4">
      <div class="flex flex-col">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">
          Liste des feuilles de temps
        </h3>
        <span class="text-xs text-gray-500 dark:text-gray-400"
          >Mise à jour il y a {{ lastUpdateMinutes }} minutes</span
        >
      </div>
      <button
        (click)="loadAllTimesheets()"
        class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
        title="Recharger les données"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
      </button>
    </div>
    <div class="flex items-center space-x-2">
      <ng-container *ngIf="selectedTimesheet">
        <button
          class="rounded-lg bg-yellow-50 px-4 py-2 text-sm font-medium text-yellow-600 transition hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50"
          (click)="openModal(true)"
        >
          Modifier
        </button>

        <button
          class="rounded-lg bg-red-50 px-4 py-2 text-sm font-medium text-red-600 transition hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50"
          (click)="deleteTimesheet()"
        >
          Supprimer
        </button>
      </ng-container>
      <button
        *ngIf="!selectedTimesheet"
        class="rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        (click)="openModal(false)"
      >
        Ajouter une feuille
      </button>
    </div>
  </div>

  <!-- Filtres -->
  <div class="mb-6 bg-gray-50 p-4 rounded-lg dark:bg-gray-700/50">
    <form
      [formGroup]="filterForm"
      (ngSubmit)="applyFilters()"
      class="flex flex-wrap gap-4 items-end"
    >
      <div class="flex flex-col" *ngIf="!employeeId">
        <label class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
          >Employé</label
        >
        <select
          formControlName="employeeId"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        >
          <option value="">Tous les employés</option>
          <option *ngFor="let employee of employees" [value]="employee.id">
            {{ getEmployeeName(employee.id) }}
          </option>
        </select>
      </div>

      <div class="flex flex-col">
        <label class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
          >Date de début</label
        >
        <input
          type="date"
          formControlName="startDate"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <div class="flex flex-col">
        <label class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
          >Date de fin</label
        >
        <input
          type="date"
          formControlName="endDate"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <div class="flex gap-2">
        <button
          type="submit"
          class="rounded-lg bg-blue-50 px-4 py-2 text-sm font-medium text-blue-600 transition hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
        >
          Filtrer
        </button>
        <button
          type="button"
          (click)="resetFilters()"
          class="rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-600 transition hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600"
        >
          Réinitialiser
        </button>
      </div>
    </form>
  </div>

  <!-- Table avec bordure légère -->
  <div
    *ngIf="!isLoading"
    class="relative overflow-x-auto rounded-lg border border-gray-100 dark:border-gray-700"
  >
    <table class="w-full table-auto">
      <!-- Table head avec fond de couleur subtil -->
      <thead
        class="bg-gray-50 text-xs uppercase text-gray-700 dark:bg-gray-700/50 dark:text-gray-300"
      >
        <tr>
          <th class="px-4 py-3 text-left">N°</th>
          <th class="px-4 py-3 text-left">Employé</th>
          <th class="px-4 py-3 text-left">Période début</th>
          <th class="px-4 py-3 text-left">Période fin</th>
          <th class="px-4 py-3 text-right">Heures régulières</th>
          <th class="px-4 py-3 text-right">Heures suppl.</th>
          <th class="px-4 py-3 text-right">Heures manquantes</th>
        </tr>
      </thead>
      <!-- Table body avec alternance de couleurs -->
      <tbody>
        <tr
          *ngFor="let timesheet of timesheets; let odd = odd"
          (click)="selectTimesheet(timesheet)"
          [ngClass]="{
            'bg-blue-100 dark:bg-blue-700 text-blue-900 dark:text-blue-200 font-semibold':
              selectedTimesheet === timesheet,
            'bg-gray-50 dark:bg-gray-800/50':
              odd && selectedTimesheet !== timesheet,
            'bg-white dark:bg-gray-800': !odd && selectedTimesheet !== timesheet
          }"
          class="border-b border-gray-100 transition hover:bg-blue-50 dark:border-gray-700 dark:hover:bg-gray-700/50"
        >
          <td class="px-4 py-3">{{ timesheet.id }}</td>
          <td class="px-4 py-3">{{ getEmployeeName(timesheet.employeeId) }}</td>
          <td class="px-4 py-3">{{ timesheet.periodStart | date }}</td>
          <td class="px-4 py-3">{{ timesheet.periodEnd | date }}</td>
          <td class="px-4 py-3 text-right">
            {{ timesheet.totalRegularHours | number : "1.0-0" }}h
          </td>
          <td class="px-4 py-3 text-right">
            {{ timesheet.totalOvertimeHours | number : "1.0-0" }}h
          </td>
          <td class="px-4 py-3 text-right">
            {{ timesheet.totalUndertimeHours | number : "1.0-0" }}h
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Message si aucune donnée -->
  <div
    *ngIf="!isLoading && timesheets.length === 0"
    class="flex justify-center items-center py-8 text-gray-500 dark:text-gray-400"
  >
    Aucune feuille de temps trouvée.
  </div>
</div>

<div
  *ngIf="showModal"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
>
  <div
    class="bg-white min-w-[50vw] rounded-lg shadow-lg p-6 w-full max-w-md mx-4 dark:bg-gray-800"
  >
    <div class="flex justify-between items-center mb-4">
      <button
        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        (click)="closeModal()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <app-timesheet-create-modal
      [employees]="employees"
      (save)="saveTimesheet($event)"
      [timesheet]="selectedTimesheet"
      (cancel)="closeModal()"
    ></app-timesheet-create-modal>
  </div>
</div>
