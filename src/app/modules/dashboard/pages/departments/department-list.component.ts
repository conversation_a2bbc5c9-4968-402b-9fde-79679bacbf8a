import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  Company,
  DepartmentResponseDto,
  PositionResponseDto,
} from 'src/app/core/models/company.model';
import { DepartmentService } from 'src/app/core/services/department/department.service';
import { PositionService } from 'src/app/core/services/department/position.service';
import { DepartmentCreateModalComponent } from './components/department-create-modal/department-create-modal.component';
import { PositionCreateModalComponent } from './components/position-create-modal/position-create-modal.component';
import { ConfirmDialogComponent } from '../../components/confirm-modal/confirm-modal.component';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-department-list',
  templateUrl: './department-list.component.html',
  standalone: true,
  imports: [
    CommonModule,
    DepartmentCreateModalComponent,
    PositionCreateModalComponent,
  ],
})
export class DepartmentListComponent implements OnInit {
  departments: DepartmentResponseDto[] = [];
  isLoading = true;
  currentCompanyId!: string;
  openMenuId: string | null = null;

  // États pour les modales
  showDepartmentModal = false;
  showPositionModal = false;
  isEditingDepartment = false;
  isEditingPosition = false;
  selectedDepartment: DepartmentResponseDto | null = null;
  selectedPosition: PositionResponseDto | null = null;
  selectedDepartmentIdForPosition: string | null = null;

  constructor(
    private departmentService: DepartmentService,
    private positionService: PositionService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.currentCompanyId = company.id!;
    });
    this.loadDepartments();
  }

  loadDepartments(): void {
    this.isLoading = true;
    this.departmentService.getAllDepartments(this.currentCompanyId).subscribe({
      next: (departments) => {
        this.departments = departments;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      },
    });
  }

  toggleDepartmentMenu(departmentId: string): void {
    this.openMenuId = this.openMenuId === departmentId ? null : departmentId;
  }

  // Méthodes pour les départements
  openDepartmentModal(
    editMode = false,
    department?: DepartmentResponseDto
  ): void {
    this.isEditingDepartment = editMode;
    this.selectedDepartment = department || null;
    this.showDepartmentModal = true;
  }

  handleDepartmentSave(data: {
    departmentName: string;
    description?: string;
  }): void {
    if (this.isEditingDepartment && this.selectedDepartment) {
      // Mise à jour du département existant
      this.departmentService
        .updateDepartment(
          this.currentCompanyId,
          this.selectedDepartment.id,
          data
        )
        .subscribe({
          next: (updatedDepartment) => {
            this.departments = this.departments.map((dept) =>
              dept.id === updatedDepartment.id ? updatedDepartment : dept
            );
            this.closeDepartmentModal();
          },
          error: () => {
            // Gérer l'erreur
          },
        });
    } else {
      // Création d'un nouveau département
      this.departmentService
        .createDepartment(this.currentCompanyId, data)
        .subscribe({
          next: (newDepartment) => {
            this.departments = [...this.departments, newDepartment];
            this.closeDepartmentModal();
          },
          error: () => {
            // Gérer l'erreur
          },
        });
    }
  }

  closeDepartmentModal(): void {
    this.showDepartmentModal = false;
    this.isEditingDepartment = false;
    this.selectedDepartment = null;
  }

  // Méthodes pour les positions
  openPositionModal(
    departmentId: string,
    editMode = false,
    position?: PositionResponseDto
  ): void {
    this.selectedDepartmentIdForPosition = departmentId;
    this.isEditingPosition = editMode;
    this.selectedPosition = position || null;
    this.showPositionModal = true;
  }

  handlePositionSave(data: {
    positionTitle: string;
    description?: string;
    requiredSkills: string[];
  }): void {
    if (!this.selectedDepartmentIdForPosition) return;

    if (this.isEditingPosition && this.selectedPosition) {
      // Mise à jour de la position existante
      this.positionService
        .updatePosition(
          this.currentCompanyId,
          this.selectedDepartmentIdForPosition,
          this.selectedPosition.id,
          data
        )
        .subscribe({
          next: (updatedPosition) => {
            this.updatePositionInDepartments(updatedPosition);
            this.closePositionModal();
          },
          error: () => {
            // Gérer l'erreur
          },
        });
    } else {
      // Création d'une nouvelle position
      this.positionService
        .createPosition(
          this.currentCompanyId,
          this.selectedDepartmentIdForPosition,
          data
        )
        .subscribe({
          next: (newPosition) => {
            this.addPositionToDepartment(newPosition);
            this.closePositionModal();
          },
          error: () => {
            // Gérer l'erreur
          },
        });
    }
  }

  closePositionModal(): void {
    this.showPositionModal = false;
    this.isEditingPosition = false;
    this.selectedPosition = null;
    this.selectedDepartmentIdForPosition = null;
  }

  // Méthodes utilitaires
  private updatePositionInDepartments(
    updatedPosition: PositionResponseDto
  ): void {
    this.departments = this.departments.map((dept) => {
      if (dept.id === updatedPosition.departmentId) {
        return {
          ...dept,
          positions:
            dept.positions?.map((pos) =>
              pos.id === updatedPosition.id ? updatedPosition : pos
            ) || [],
        };
      }
      return dept;
    });
  }

  private addPositionToDepartment(newPosition: PositionResponseDto): void {
    this.departments = this.departments.map((dept) => {
      if (dept.id === newPosition.departmentId) {
        return {
          ...dept,
          positions: [...(dept.positions || []), newPosition],
        };
      }
      return dept;
    });
  }

  // ... (les méthodes deleteDepartment et deletePosition restent inchangées)
}
