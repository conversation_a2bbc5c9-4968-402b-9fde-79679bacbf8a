<!-- department-list.component.html -->
<div class="flex flex-col rounded-xl bg-white p-6 dark:bg-gray-800">
  <!-- Header -->
  <div class="mb-6 flex items-center justify-between">
    <div class="flex flex-col">
      <h3 class="text-lg font-bold text-gray-800 dark:text-white">
        Gestion des départements
      </h3>
      <span class="text-xs text-gray-500 dark:text-gray-400"
        >{{ departments.length }} département(s)</span
      >
    </div>
    <button
      (click)="openDepartmentModal()"
      class="flex items-center gap-2 rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 4v16m8-8H4"
        />
      </svg>
      Nouveau département
    </button>
  </div>

  <!-- Loading state -->
  <div
    *ngIf="isLoading"
    class="flex flex-col items-center justify-center py-12"
  >
    <div
      class="h-10 w-10 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"
    ></div>
    <p class="mt-3 text-sm text-gray-600 dark:text-gray-400">
      Chargement des départements...
    </p>
  </div>

  <!-- Empty state -->
  <div
    *ngIf="!isLoading && departments.length === 0"
    class="flex flex-col items-center justify-center py-12"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-12 w-12 text-gray-400"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="1.5"
        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      />
    </svg>
    <h3 class="mt-3 text-lg font-medium text-gray-700 dark:text-gray-300">
      Aucun département créé
    </h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Commencez par créer votre premier département
    </p>
    <button
      (click)="openDepartmentModal()"
      class="mt-4 flex items-center gap-2 rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 4v16m8-8H4"
        />
      </svg>
      Créer un département
    </button>
  </div>

  <!-- Departments grid -->
  <div
    *ngIf="!isLoading && departments.length > 0"
    class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
  >
    <div
      *ngFor="let department of departments"
      class="rounded-xl border border-gray-200 bg-white shadow-sm transition-all hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
    >
      <div class="p-5">
        <!-- Department header -->
        <div class="flex items-start justify-between">
          <div>
            <h3
              class="text-lg font-semibold text-gray-800 dark:text-white"
              [title]="department.departmentName"
            >
              {{ department.departmentName }}
            </h3>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ department.positions?.length || 0 }} poste(s)
            </p>
          </div>
          <div class="relative">
            <button
              (click)="toggleDepartmentMenu(department.id)"
              class="rounded-lg p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:hover:bg-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"
                />
              </svg>
            </button>

            <!-- Department dropdown menu -->
            <div
              *ngIf="openMenuId === department.id"
              class="absolute right-0 z-10 mt-1 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700"
            >
              <button
                (click)="
                  openDepartmentModal(true, department); openMenuId = null
                "
                class="flex w-full items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
                Modifier
              </button>
              <button
                class="flex w-full items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
                Supprimer
              </button>
            </div>
          </div>
        </div>

        <!-- Department description -->
        <p
          class="mt-2 text-sm text-gray-600 dark:text-gray-400"
          [title]="department.description || 'Aucune description'"
        >
          {{ department.description || "Aucune description" }}
        </p>

        <!-- Positions section -->
        <div class="mt-4">
          <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Postes
            </h4>
            <button
              (click)="openPositionModal(department.id)"
              class="flex items-center gap-1 rounded-lg bg-blue-50 px-2 py-1 text-xs font-medium text-blue-600 transition hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-3 w-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Ajouter
            </button>
          </div>

          <!-- Empty positions state -->
          <div
            *ngIf="!department.positions || department.positions.length === 0"
            class="mt-2 rounded-lg bg-gray-50 p-3 text-center text-sm text-gray-500 dark:bg-gray-700/50 dark:text-gray-400"
          >
            Aucun poste dans ce département
          </div>

          <!-- Positions list -->
          <div
            *ngIf="department.positions && department.positions.length > 0"
            class="mt-2 space-y-2"
          >
            <div
              *ngFor="let position of department.positions"
              class="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
            >
              <div class="flex items-start justify-between">
                <div>
                  <h5
                    class="text-sm font-medium text-gray-800 dark:text-white"
                    [title]="position.positionTitle"
                  >
                    {{ position.positionTitle }}
                  </h5>
                  <p
                    *ngIf="position.description"
                    class="mt-1 text-xs text-gray-600 dark:text-gray-400"
                    [title]="position.description"
                  >
                    {{ position.description }}
                  </p>
                </div>
                <div class="flex gap-1">
                  <button
                    (click)="openPositionModal(department.id, true, position)"
                    class="rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:hover:bg-gray-700"
                    title="Modifier"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                  </button>
                  <button
                    class="rounded p-1 text-red-500 hover:bg-gray-100 hover:text-red-700 dark:hover:bg-gray-700"
                    title="Supprimer"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Skills chips -->
              <div
                *ngIf="
                  position.requiredSkills && position.requiredSkills.length > 0
                "
                class="mt-2 flex flex-wrap gap-1"
              >
                <span
                  *ngFor="let skill of position.requiredSkills"
                  class="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                  >{{ skill }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modale de département -->
<app-department-create-modal
  *ngIf="showDepartmentModal"
  [isEditing]="isEditingDepartment"
  [existingDepartment]="selectedDepartment"
  (save)="handleDepartmentSave($event)"
  (close)="closeDepartmentModal()"
></app-department-create-modal>

<!-- Modale de position -->
<app-position-create-modal
  *ngIf="showPositionModal"
  [isEditing]="isEditingPosition"
  [existingPosition]="selectedPosition"
  (save)="handlePositionSave($event)"
  (close)="closePositionModal()"
></app-position-create-modal>
