// position-create-modal.component.ts
import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  NgModule,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  NgModel,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { PositionResponseDto } from 'src/app/core/models/company.model';

@Component({
  selector: 'app-position-create-modal',
  templateUrl: './position-create-modal.component.html',
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  standalone: true,
})
export class PositionCreateModalComponent implements OnInit {
  @Input() isEditing = false;
  @Input() existingPosition: PositionResponseDto | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<{
    positionTitle: string;
    description?: string;
    requiredSkills: string[];
  }>();

  positionForm!: FormGroup;
  skillInput = '';

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.positionForm = this.fb.group({
      positionTitle: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      requiredSkills: [[]],
    });

    if (this.existingPosition && this.isEditing) {
      this.positionForm.patchValue({
        positionTitle: this.existingPosition.positionTitle,
        description: this.existingPosition.description,
        requiredSkills: this.existingPosition.requiredSkills || [],
      });
    }
  }

  closeModal(): void {
    this.close.emit();
  }

  addSkill(): void {
    if (this.skillInput.trim()) {
      const currentSkills =
        this.positionForm.get('requiredSkills')?.value || [];
      if (!currentSkills.includes(this.skillInput.trim())) {
        this.positionForm
          .get('requiredSkills')
          ?.setValue([...currentSkills, this.skillInput.trim()]);
      }
      this.skillInput = '';
    }
  }

  removeSkill(skill: string): void {
    const currentSkills = this.positionForm.get('requiredSkills')?.value || [];
    this.positionForm
      .get('requiredSkills')
      ?.setValue(currentSkills.filter((s: string) => s !== skill));
  }

  savePosition(): void {
    if (this.positionForm.valid) {
      const formValue = this.positionForm.value;
      this.save.emit({
        positionTitle: formValue.positionTitle,
        description: formValue.description,
        requiredSkills: formValue.requiredSkills,
      });
      this.closeModal();
    }
  }
}
