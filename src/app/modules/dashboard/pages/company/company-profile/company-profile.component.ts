// company-profile.component.ts
import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { select, Store } from '@ngrx/store';

import { switchMap } from 'rxjs/operators';
import {
  BonusCalculationEnum,
  Company,
  CompanyTaxSettings,
  Department,
  PayrollConfiguration,
  PayrollCycleEnum,
  TaxPaymentFrequencyEnum,
} from 'src/app/core/models/company.model';
import { CompanyService } from 'src/app/core/services/company/company.service';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-company-profile',
  templateUrl: './company-profile.component.html',
  imports: [DatePipe, CommonModule, FormsModule, ReactiveFormsModule],
})
export class CompanyProfileComponent implements OnInit {
  company!: Company;
  isEditMode = false;
  isTaxEditMode = false;
  isPayrollEditMode = false;
  showDepartmentModal = false;
  showDocumentModal = false;
  editingDepartment: Department | null = null;
  selectedFile: File | null = null;

  activeTab = 'general';
  tabs = [
    { id: 'general', name: 'Informations générales', icon: 'info' },
    { id: 'tax', name: 'Paramètres fiscaux', icon: 'receipt' },
    { id: 'payroll', name: 'Configuration de paie', icon: 'currency' },
    { id: 'employees', name: 'Employés', icon: 'users' },
    { id: 'departments', name: 'Départements', icon: 'building' },
    { id: 'documents', name: 'Documents', icon: 'folder' },
  ];

  editForm!: FormGroup;
  taxForm!: FormGroup;
  payrollForm!: FormGroup;
  departmentForm!: FormGroup;
  documentForm!: FormGroup;

  constructor(
    private route: ActivatedRoute,
    private companyService: CompanyService,
    private fb: FormBuilder,
    private store: Store<AppState>
  ) {
    this.createForms();
  }

  ngOnInit(): void {
    this.store
      .pipe(select(selectCurrentCompany))
      .pipe(
        switchMap((company) => {
          return this.companyService.getCompanyById(company.id!);
        })
      )
      .subscribe((company) => {
        this.company = company;
        this.updateForms();
      });
  }

  createForms(): void {
    this.editForm = this.fb.group({
      companyName: ['', Validators.required],
      officialName: ['', Validators.required],
      industry: ['', Validators.required],
      taxIdentificationNumber: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phoneNumbers: [''],
      website: [''],
      street: [''],
      city: [''],
      postalCode: [''],
      country: [''],
      description: [''],
    });

    this.taxForm = this.fb.group({
      incomeTaxRate: [
        0,
        [Validators.required, Validators.min(0), Validators.max(100)],
      ],
      socialSecurityRate: [
        0,
        [Validators.required, Validators.min(0), Validators.max(100)],
      ],
      unEmploymentInsuranceRate: [
        0,
        [Validators.required, Validators.min(0), Validators.max(100)],
      ],
      healthInsuranceRate: [
        0,
        [Validators.required, Validators.min(0), Validators.max(100)],
      ],
      pensionContributionRate: [
        0,
        [Validators.required, Validators.min(0), Validators.max(100)],
      ],
      taxPaymentFrequency: ['MONTHLY', Validators.required],
    });

    this.payrollForm = this.fb.group({
      payrollCycle: ['MONTHLY', Validators.required],
      paymentDay: [
        1,
        [Validators.required, Validators.min(1), Validators.max(31)],
      ],
      overtimeMultiplier: [1.5, [Validators.required, Validators.min(1)]],
      bonusType: ['PERCENTAGE_OF_SALARY'],
    });

    this.departmentForm = this.fb.group({
      departmentName: ['', Validators.required],
      description: [''],
    });

    this.documentForm = this.fb.group({
      name: ['', Validators.required],
      type: ['OTHER', Validators.required],
      description: [''],
    });
  }

  updateForms(): void {
    if (this.company) {
      this.editForm.patchValue({
        companyName: this.company.companyName,
        officialName: this.company.officialName,
        industry: this.company.industry,
        taxIdentificationNumber: this.company.taxIdentificationNumber,
        email: this.company.email,
        phoneNumbers: this.company.phoneNumbers?.join(', '),
        website: this.company.website,
        street: this.company.address?.street,
        city: this.company.address?.city,
        postalCode: this.company.address?.postalCode,
        country: this.company.address?.country,
        description: this.company.description,
      });

      if (this.company.taxSettings) {
        this.taxForm.patchValue({
          incomeTaxRate: this.company.taxSettings.incomeTaxRate,
          socialSecurityRate: this.company.taxSettings.socialSecurityRate,
          unEmploymentInsuranceRate:
            this.company.taxSettings.unEmploymentInsuranceRate,
          healthInsuranceRate: this.company.taxSettings.healthInsuranceRate,
          pensionContributionRate:
            this.company.taxSettings.pensionContributionRate,
          taxPaymentFrequency: this.company.taxSettings.taxPaymentFrequency,
        });
      }

      if (this.company.payrollConfiguration) {
        this.payrollForm.patchValue({
          payrollCycle: this.company.payrollConfiguration.payrollCycle,
          paymentDay: this.company.payrollConfiguration.paymentDay,
          overtimeMultiplier:
            this.company.payrollConfiguration.overtimeMultiplier,
          bonusType: this.company.payrollConfiguration.bonusType,
        });
      }
    }
  }

  toggleEditMode(): void {
    this.isEditMode = !this.isEditMode;
    if (this.isEditMode) {
      this.updateForms();
    }
  }

  toggleTaxEditMode(): void {
    this.isTaxEditMode = !this.isTaxEditMode;
    if (this.isTaxEditMode && this.company.taxSettings) {
      this.taxForm.patchValue(this.company.taxSettings);
    }
  }

  togglePayrollEditMode(): void {
    this.isPayrollEditMode = !this.isPayrollEditMode;
    if (this.isPayrollEditMode && this.company.payrollConfiguration) {
      this.payrollForm.patchValue(this.company.payrollConfiguration);
    }
  }

  saveCompany(): void {
    if (this.editForm.valid) {
      const formValue = this.editForm.value;
      const updatedCompany = {
        ...this.company,
        ...formValue,
        phoneNumbers: formValue.phoneNumbers
          .split(',')
          .map((phone: string) => phone.trim()),
        address: {
          street: formValue.street,
          city: formValue.city,
          postalCode: formValue.postalCode,
          country: formValue.country,
        },
      };

      this.companyService
        .updateCompany(this.company.id!, updatedCompany)
        .subscribe(
          (company) => {
            this.company = company;
            this.isEditMode = false;
            // Afficher un message de succès
          },
          (error) => {
            // Afficher un message d'erreur
          }
        );
    }
  }

  saveTaxSettings(): void {
    if (this.taxForm.valid) {
      const taxSettings: CompanyTaxSettings = {
        ...this.company.taxSettings,
        ...this.taxForm.value,
      };

      this.companyService
        .updateCompany(this.company.id!, { taxSettings })
        .subscribe(
          (settings) => {
            this.company.taxSettings = settings;
            this.isTaxEditMode = false;
            // Afficher un message de succès
          },
          (error) => {
            // Afficher un message d'erreur
          }
        );
    }
  }

  savePayrollConfig(): void {
    if (this.payrollForm.valid) {
      const payrollConfig: PayrollConfiguration = {
        ...this.company.payrollConfiguration,
        ...this.payrollForm.value,
      };

      this.companyService
        .updateCompany(this.company.id!, { payrollConfig })
        .subscribe(
          (config) => {
            this.company.payrollConfiguration = config;
            this.isPayrollEditMode = false;
            // Afficher un message de succès
          },
          (error) => {
            // Afficher un message d'erreur
          }
        );
    }
  }

  initializeTaxSettings(): void {
    const defaultTaxSettings: CompanyTaxSettings = {
      incomeTaxRate: 20,
      socialSecurityRate: 15,
      unEmploymentInsuranceRate: 2,
      healthInsuranceRate: 5,
      pensionContributionRate: 10,
      taxPaymentFrequency: TaxPaymentFrequencyEnum.MONTHLY,
    };

    this.companyService
      .updateCompany(this.company.id!, { defaultTaxSettings })
      .subscribe(
        (settings) => {
          this.company.taxSettings = settings;
          // Afficher un message de succès
        },
        (error) => {
          // Afficher un message d'erreur
        }
      );
  }

  initializePayrollConfig(): void {
    const defaultPayrollConfig: PayrollConfiguration = {
      payrollCycle: PayrollCycleEnum.MONTHLY,
      paymentDay: 5,
      overtimeMultiplier: 1.5,
      bonusType: BonusCalculationEnum.PERCENTAGE_OF_SALARY,
    };

    this.companyService
      .updateCompany(this.company.id!, { defaultPayrollConfig })
      .subscribe(
        (config) => {
          this.company.payrollConfiguration = config;
          // Afficher un message de succès
        },
        (error) => {
          // Afficher un message d'erreur
        }
      );
  }

  // Gestion des départements
  openAddDepartmentModal(): void {
    this.editingDepartment = null;
    this.departmentForm.reset();
    this.showDepartmentModal = true;
  }

  editDepartment(department: Department): void {
    this.editingDepartment = department;
    this.departmentForm.patchValue({
      departmentName: department.departmentName,
      description: department.description,
    });
    this.showDepartmentModal = true;
  }

  closeDepartmentModal(): void {
    this.showDepartmentModal = false;
  }

  submitDepartmentForm(): void {
    if (this.departmentForm.valid) {
      const departmentData = this.departmentForm.value;

      if (this.editingDepartment) {
        // Mettre à jour le département
        this.companyService
          .updateCompany(this.company.id!, { departmentData })
          .subscribe(
            (updatedDepartment) => {
              const index = this.company.departments?.findIndex(
                (d) => d.id === updatedDepartment.id
              );
              if (index !== -1 && this.company.departments) {
                this.company.departments[index!] = updatedDepartment;
              }
              this.showDepartmentModal = false;
            },
            (error) => {
              // Afficher un message d'erreur
            }
          );
      } else {
        // Créer un nouveau département
        this.companyService
          .updateCompany(this.company.id!, departmentData)
          .subscribe(
            (newDepartment) => {
              this.company.departments = [
                ...(this.company.departments || []),
                newDepartment,
              ];
              this.showDepartmentModal = false;
            },
            (error) => {
              // Afficher un message d'erreur
            }
          );
      }
    }
  }

  deleteDepartment(departmentId: string): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce département ?')) {
      this.companyService
        .updateCompany(this.company.id!, departmentId)
        .subscribe(
          () => {
            this.company.departments = this.company.departments?.filter(
              (d) => d.id !== departmentId
            );
            // Afficher un message de succès
          },
          (error) => {
            // Afficher un message d'erreur
          }
        );
    }
  }

  getEmployeeCountForDepartment(departmentId: string): number {
    return (
      this.company.employees?.filter((e) => e.department?.id === departmentId)
        .length || 0
    );
  }

  // Gestion des documents
  openUploadDocumentModal(): void {
    this.documentForm.reset();
    this.selectedFile = null;
    this.showDocumentModal = true;
  }

  closeDocumentModal(): void {
    this.showDocumentModal = false;
  }

  onFileSelected(event: any): void {
    this.selectedFile = event.target.files[0];
  }

  submitDocumentForm(): void {}

  deleteDocument(documentId: string): void {}

  getTabIcon(tabId: string): string {
    const icons: { [key: string]: string } = {
      general: '📋',
      tax: '🧾',
      payroll: '💰',
      employees: '👥',
      departments: '🏢',
      documents: '📁',
    };
    return icons[tabId] || 'ℹ️';
  }
}
