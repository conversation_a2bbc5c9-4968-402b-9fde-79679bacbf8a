<div class="flex flex-col rounded-xl bg-white px-6 py-6 dark:bg-gray-800">
  <!-- Header avec titre et bouton d'ajout -->
  <div class="mb-6 flex items-center justify-between">
    <div class="flex items-center gap-4">
      <div class="flex flex-col">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">
          Liste des demandes de congé
        </h3>
        <span class="text-xs text-gray-500 dark:text-gray-400">
          Mise à jour il y a {{ lastUpdateMinutes }} minutes
        </span>
      </div>
      <button
        (click)="loadAllLeaves()"
        class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
        title="Recharger les données"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
      </button>
    </div>
    <div class="flex items-center space-x-2">
      <ng-container *ngIf="selectedLeave">
        <button
          class="rounded-lg bg-yellow-50 px-4 py-2 text-sm font-medium text-yellow-600 transition hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50"
          (click)="openModal(true)"
        >
          Modifier
        </button>

        <button
          class="rounded-lg bg-red-50 px-4 py-2 text-sm font-medium text-red-600 transition hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50"
          (click)="deleteLeave()"
        >
          Supprimer
        </button>
      </ng-container>
      <button
        *ngIf="!selectedLeave"
        class="rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        (click)="openModal(false)"
      >
        Nouvelle demande
      </button>
    </div>
  </div>

  <!-- Filtres -->
  <div class="mb-6 bg-gray-50 p-4 rounded-lg dark:bg-gray-700/50">
    <form
      [formGroup]="filterForm"
      (ngSubmit)="applyFilters()"
      class="flex flex-wrap gap-4 items-end"
    >
      <div class="flex flex-col">
        <label
          class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
        >
          Employé
        </label>
        <select
          formControlName="employeeId"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        >
          <option value="">Tous les employés</option>
          <option *ngFor="let employee of employees" [value]="employee.id">
            {{ getEmployeeName(employee.id) }}
          </option>
        </select>
      </div>

      <div class="flex flex-col">
        <label
          class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
        >
          Date de début
        </label>
        <input
          type="date"
          formControlName="startDate"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <div class="flex flex-col">
        <label
          class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
        >
          Date de fin
        </label>
        <input
          type="date"
          formControlName="endDate"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <div class="flex flex-col">
        <label
          class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
        >
          Statut
        </label>
        <select
          formControlName="status"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        >
          <option value="">Tous les statuts</option>
          <option value="PENDING">En attente</option>
          <option value="APPROVED">Approuvé</option>
          <option value="REJECTED">Rejeté</option>
        </select>
      </div>

      <div class="flex gap-2">
        <button
          type="submit"
          class="rounded-lg bg-blue-50 px-4 py-2 text-sm font-medium text-blue-600 transition hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
        >
          Filtrer
        </button>
        <button
          type="button"
          (click)="resetFilters()"
          class="rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-600 transition hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600"
        >
          Réinitialiser
        </button>
      </div>
    </form>
  </div>

  <!-- Tableau des demandes de congé -->
  <div
    *ngIf="!isLoading"
    class="relative overflow-x-auto rounded-lg border border-gray-100 dark:border-gray-700"
  >
    <table class="w-full table-auto">
      <thead
        class="bg-gray-50 text-xs uppercase text-gray-700 dark:bg-gray-700/50 dark:text-gray-300"
      >
        <tr>
          <th class="px-4 py-3 text-left">N°</th>
          <th class="px-4 py-3 text-left">Employé</th>
          <th class="px-4 py-3 text-left">Type</th>
          <th class="px-4 py-3 text-left">Date de début</th>
          <th class="px-4 py-3 text-left">Date de fin</th>
          <th class="px-4 py-3 text-right">Durée</th>
          <th class="px-4 py-3 text-left">Statut</th>
          <th class="px-4 py-3 text-left">Motif</th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let leave of leaves; let odd = odd"
          (click)="selectLeave(leave)"
          [ngClass]="{
            'bg-blue-100 dark:bg-blue-700 text-blue-900 dark:text-blue-200 font-semibold':
              selectedLeave === leave,
            'bg-gray-50 dark:bg-gray-800/50': odd && selectedLeave !== leave,
            'bg-white dark:bg-gray-800': !odd && selectedLeave !== leave
          }"
          class="border-b border-gray-100 transition hover:bg-blue-50 dark:border-gray-700 dark:hover:bg-gray-700/50"
        >
          <td class="px-4 py-3">{{ leave.id }}</td>
          <td class="px-4 py-3">
            {{ getEmployeeName(leave.employeeId) }}
          </td>
          <td class="px-4 py-3">{{ leave.leaveType | titlecase }}</td>
          <td class="px-4 py-3">
            {{ leave.startDate | date : "dd/MM/yyyy" }}
          </td>
          <td class="px-4 py-3">
            {{ leave.endDate | date : "dd/MM/yyyy" }}
          </td>
          <td class="px-4 py-3 text-right">
            {{ calculateDuration(leave.startDate, leave.endDate) }} jours
          </td>
          <td class="px-4 py-3">
            <span
              [ngClass]="{
                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400':
                  leave.status === 'PENDING',
                'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400':
                  leave.status === 'APPROVED',
                'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400':
                  leave.status === 'REJECTED'
              }"
              class="rounded-full px-2.5 py-0.5 text-xs font-medium"
            >
              {{
                leave.status === "PENDING"
                  ? "En attente"
                  : leave.status === "APPROVED"
                  ? "Approuvé"
                  : "Rejeté"
              }}
            </span>
          </td>
          <td class="px-4 py-3">{{ leave.reason }}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Message si aucune donnée -->
  <div
    *ngIf="!isLoading && leaves.length === 0"
    class="flex justify-center items-center py-8 text-gray-500 dark:text-gray-400"
  >
    Aucune demande de congé trouvée.
  </div>
</div>

<!-- Modal component will be added here -->
