import { Component, OnInit } from '@angular/core';
import { EmployeeStat } from '../../models/employee';
import { OverviewHeaderComponent } from '../../components/overview/overview-header/overview-header.component';
import { EmployeeDataCardComponent } from '../../components/overview/employee-data-card/employee-data-card.component';
import { OverviewChartCardComponent } from '../../components/overview/overview-chart-card/overview-chart-card.component';
import { OverviewEmployeesTableComponent } from '../../components/overview/overview-employees-table/overview-employees-table.component';
import { OverviewLeaveRequestTableComponent } from '../../components/overview/overview-leave-request-table/overview-leave-request-table.component';
import { UpcomingEventsComponent } from '../../components/overview/upcoming-events/upcoming-events.component';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { Company } from 'src/app/core/models/company.model';

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  imports: [
    OverviewHeaderComponent,
    EmployeeDataCardComponent,
    OverviewChartCardComponent,
    OverviewEmployeesTableComponent,
    OverviewLeaveRequestTableComponent,
    UpcomingEventsComponent,
  ],
})
export class OverviewComponent implements OnInit {
  employeeData!: EmployeeStat[];
  company!: Company;

  constructor(
    private employeeService: EmployeeService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
    });
    this.loadEmployeeStatistics();
  }

  /**
   * Charge les statistiques des employés et calcule les pourcentages
   * en comparant le mois actuel avec le mois précédent
   */
  loadEmployeeStatistics(): void {
    // Mois actuel
    const currentDate = new Date();
    // Mois précédent
    const previousDate = new Date();
    previousDate.setMonth(previousDate.getMonth() - 1);

    // Récupération des statistiques pour le mois actuel
    this.employeeService
      .getEmployeeStatistics(this.company.id!, currentDate)
      .subscribe((currentStats) => {
        // Récupération des statistiques pour le mois précédent
        this.employeeService
          .getEmployeeStatistics(this.company.id!, previousDate)
          .subscribe((previousStats) => {
            // Calcul des pourcentages de variation
            const totalEmployeesPercentage = this.calculatePercentageChange(
              previousStats.totalEmployees,
              currentStats.totalEmployees
            );

            const newlyHiredPercentage = this.calculatePercentageChange(
              previousStats.newlyHired,
              currentStats.newlyHired
            );

            const resignedPercentage = this.calculatePercentageChange(
              previousStats.resigned,
              currentStats.resigned
            );

            const onLeavePercentage = this.calculatePercentageChange(
              previousStats.onLeave,
              currentStats.onLeave
            );

            this.employeeData = [
              {
                icon: 'assets/icons/heroicons/outline/users.svg',
                title: 'Total des employés',
                value: currentStats.totalEmployees,
                percentage: totalEmployeesPercentage,
                change:
                  totalEmployeesPercentage >= 0 ? 'augmentation' : 'diminution',
                progress: this.calculateProgressValue(totalEmployeesPercentage),
                color: 'purple',
              },
              {
                icon: 'assets/icons/heroicons/outline/user-plus.svg',
                title: 'Nouvelles embauches',
                value: currentStats.newlyHired,
                percentage: newlyHiredPercentage,
                change:
                  newlyHiredPercentage >= 0 ? 'augmentation' : 'diminution',
                progress: this.calculateProgressValue(newlyHiredPercentage),
                color: 'green',
              },
              {
                icon: 'assets/icons/heroicons/outline/user-minus.svg',
                title: 'Démissions',
                value: currentStats.resigned,
                percentage: resignedPercentage,
                change: resignedPercentage >= 0 ? 'augmentation' : 'diminution',
                progress: this.calculateProgressValue(resignedPercentage),
                color: 'red',
              },
              {
                icon: 'assets/icons/heroicons/outline/chart-pie.svg',
                title: 'Employés en congé',
                value: currentStats.onLeave,
                percentage: onLeavePercentage,
                change: onLeavePercentage >= 0 ? 'augmentation' : 'diminution',
                progress: this.calculateProgressValue(onLeavePercentage),
                color: 'orange',
              },
            ];
          });
      });
  }

  /**
   * Calcule le pourcentage de changement entre deux valeurs
   * @param oldValue La valeur précédente
   * @param newValue La valeur actuelle
   * @returns Le pourcentage de changement arrondi à deux décimales
   */
  private calculatePercentageChange(
    oldValue: number,
    newValue: number
  ): number {
    if (oldValue === 0) {
      return newValue > 0 ? 100 : 0;
    }

    const percentageChange = ((newValue - oldValue) / oldValue) * 100;
    return Number(percentageChange.toFixed(2));
  }

  /**
   * Calcule une valeur de progression pour la barre de progrès
   * basée sur le pourcentage de changement
   * @param percentageChange Le pourcentage de changement
   * @returns Une valeur entre 0 et 100 pour la barre de progrès
   */
  private calculateProgressValue(percentageChange: number): number {
    // Une approche simple est de transformer le pourcentage en valeur entre 0 et 100
    const absPercentage = Math.abs(percentageChange);

    // Limiter la valeur maximale à 100% pour la barre de progrès
    return Math.min(absPercentage, 100);
  }
}
