import {
  Company,
  Department,
  Position,
} from 'src/app/core/models/company.model';
import { Leave } from 'src/app/core/models/leave.model';
import { Payslip } from 'src/app/core/services/payslip/payslip.service';
import { Salary } from 'src/app/core/services/salary/salary.service';

export interface EmployeeStat {
  color: string;
  icon: string;
  title: string;
  value: number;
  percentage: number;
  change: string;
  progress: number;
}

export interface EmployeeData {
  id: string;
  hireDate: string; // Date au format ISO 8601 (string)
  position?: Position;
  companyId: number;
  salaries: Salary[];
  payslips: Payslip[];
  leaves: Leave[];
  trainings: any[];
  timesheets: any[];
  company: Company;
  department?: Department;
  performanceEvaluations: any[];
  contract?: {
    startDate: string;
    endDate?: string;
    type: string;
    status: string;
  };
  user: {
    id?: string;
    email?: string;
    password?: string;
    googleId?: string | null;
    verified?: boolean;
    verificationOtp?: string;
    role?: string;
    passwordReset?: {
      otp: string | null;
      expiresAt: string | null;
    };
    profile?: {
      id?: number;
      firstName?: string;
      lastName?: string;
      avatar?: string | null; // Avatar de l'utilisateur, peut être null
      phoneNumber?: string | null;
      birthDate?: string | null;
      addressId?: number | null;
      userId?: number;
      address?: string | null;
    };
  };
}
