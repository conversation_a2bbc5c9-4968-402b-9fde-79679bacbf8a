<div class="min-h-screen w-screen relative overflow-hidden font-inter">
  <!-- Image de fond de la plateforme avec effet flou -->
  <div class="absolute inset-0 z-0">
    <div
      class="absolute inset-0 bg-cover bg-center bg-no-repeat"
      style="background-image: url('assets/images/mockup.png')"
    ></div>
    <!-- Overlay sombre pour améliorer la lisibilité -->
    <div class="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
  </div>

  <!-- Logo en haut à gauche -->
  <div class="absolute top-6 left-6 z-30">
    <img
      src="assets/logo-w.png"
      class="h-8 transition-transform duration-300 hover:scale-105"
      alt="Logo LuminaHrGlobal"
    />
  </div>

  <!-- Composant d'authentification centré -->
  <div class="relative z-20 min-h-screen flex items-center justify-center p-6">
    <div class="w-full max-w-md">
      <!-- Conteneur du formulaire avec glassmorphism -->
      <div
        class="bg-white/80 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl p-8"
      >
        <!-- Logo pour mobile centré -->
        <!-- <div class="mb-8 flex justify-center">
          <img
            src="assets/logo.png"
            class="h-12 transition-transform duration-300 hover:scale-105"
            alt="Logo LuminaHrGlobal"
          />
        </div> -->

        <!-- Contenu du formulaire -->
        <div class="space-y-6">
          <router-outlet></router-outlet>
        </div>
      </div>

      <!-- Footer -->
      <div class="mt-8 text-center text-xs text-white/70">
        <p>© 2025 LuminaHrGlobal. Tous droits réservés.</p>
        <div class="flex justify-center mt-3 space-x-4">
          <a
            href="#"
            class="text-white/60 hover:text-white/90 transition-colors hover:scale-105 transform duration-300"
          >
            Mentions légales
          </a>
          <span class="text-white/40">•</span>
          <a
            href="#"
            class="text-white/60 hover:text-white/90 transition-colors hover:scale-105 transform duration-300"
          >
            Confidentialité
          </a>
          <span class="text-white/40">•</span>
          <a
            href="#"
            class="text-white/60 hover:text-white/90 transition-colors hover:scale-105 transform duration-300"
          >
            Support
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
