<div class="flex h-screen w-screen overflow-hidden font-inter">
  <!-- Section gauche (fond et éléments visuels) -->
  <div class="auth-bg hidden flex-1 relative bg-primary bg-cover lg:flex">
    <!-- Logo repositionné en haut à gauche avec animation -->
    <div class="absolute top-8 left-8 z-30 animate-fade-in-slide">
      <img
        src="assets/logo-w.png"
        class="w-32 transition-transform duration-300 hover:scale-105"
        alt="Logo LuminaHrGlobal"
      />
    </div>

    <!-- Contenu principal épuré -->
    <div
      class="w-full h-full flex items-center justify-center relative overflow-hidden"
    >
      <!-- Effets de dégradé subtils -->
      <div
        class="absolute top-0 left-0 w-80 h-80 bg-white/5 rounded-full filter blur-3xl"
      ></div>
      <div
        class="absolute bottom-0 right-0 w-80 h-80 bg-white/5 rounded-full filter blur-3xl"
      ></div>

      <!-- Contenu central minimaliste -->
      <div
        class="max-w-6xl space-y-12 p-8 text-center text-white z-10 animate-fade-in"
      >
        <!-- Graphique/Illustration professionnelle -->
        <div class="relative">
          <!-- Dashboard professionnel moderne -->
          <div
            class="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-lg rounded-3xl p-8 border border-white/30 shadow-2xl w-full max-w-5xl mx-auto min-h-80"
          >
            <!-- Header professionnel -->
            <div class="flex items-center justify-between mb-8">
              <div class="flex items-center space-x-3">
                <div
                  class="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center"
                >
                  <svg
                    class="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-white/90 font-semibold text-sm">
                    Dashboard RH
                  </h3>
                  <p class="text-white/60 text-xs">Vue d'ensemble</p>
                </div>
              </div>
              <div class="flex space-x-2">
                <div
                  class="w-2 h-2 bg-green-400 rounded-full animate-pulse"
                ></div>
                <span class="text-white/70 text-xs">En ligne</span>
              </div>
            </div>

            <!-- Métriques principales -->
            <div class="grid grid-cols-3 gap-6 mb-8">
              <!-- Employés -->
              <div
                class="bg-gradient-to-br from-blue-500/20 to-blue-600/10 rounded-xl p-4 border border-blue-400/20"
              >
                <div class="flex items-center justify-between mb-3">
                  <div
                    class="w-10 h-10 bg-blue-500/30 rounded-lg flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-blue-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                  <span class="text-green-300 text-xs font-medium">+12%</span>
                </div>
                <div class="text-white/90 text-2xl font-bold">247</div>
                <div class="text-white/60 text-xs">Employés actifs</div>
              </div>

              <!-- Congés -->
              <div
                class="bg-gradient-to-br from-purple-500/20 to-purple-600/10 rounded-xl p-4 border border-purple-400/20"
              >
                <div class="flex items-center justify-between mb-3">
                  <div
                    class="w-10 h-10 bg-purple-500/30 rounded-lg flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-purple-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <span class="text-orange-300 text-xs font-medium">-3%</span>
                </div>
                <div class="text-white/90 text-2xl font-bold">18</div>
                <div class="text-white/60 text-xs">Demandes en attente</div>
              </div>

              <!-- Performance -->
              <div
                class="bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 rounded-xl p-4 border border-emerald-400/20"
              >
                <div class="flex items-center justify-between mb-3">
                  <div
                    class="w-10 h-10 bg-emerald-500/30 rounded-lg flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-emerald-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                      />
                    </svg>
                  </div>
                  <span class="text-green-300 text-xs font-medium">+8%</span>
                </div>
                <div class="text-white/90 text-2xl font-bold">94%</div>
                <div class="text-white/60 text-xs">Satisfaction globale</div>
              </div>
            </div>

            <!-- Graphique moderne -->
            <div class="bg-white/5 rounded-xl p-6 border border-white/10">
              <div class="flex items-center justify-between mb-4">
                <h4 class="text-white/80 font-medium text-sm">
                  Évolution mensuelle
                </h4>
                <div class="flex space-x-2">
                  <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                  <div class="w-3 h-3 bg-purple-400 rounded-full"></div>
                  <div class="w-3 h-3 bg-emerald-400 rounded-full"></div>
                </div>
              </div>
              <div class="flex items-end justify-between space-x-2 h-20">
                <div class="flex flex-col items-center space-y-1">
                  <div
                    class="w-8 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-lg"
                    style="height: 60%"
                  ></div>
                  <span class="text-white/50 text-xs">Jan</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                  <div
                    class="w-8 bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-lg"
                    style="height: 80%"
                  ></div>
                  <span class="text-white/50 text-xs">Fév</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                  <div
                    class="w-8 bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-t-lg"
                    style="height: 100%"
                  ></div>
                  <span class="text-white/50 text-xs">Mar</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                  <div
                    class="w-8 bg-gradient-to-t from-cyan-500 to-cyan-400 rounded-t-lg"
                    style="height: 70%"
                  ></div>
                  <span class="text-white/50 text-xs">Avr</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                  <div
                    class="w-8 bg-gradient-to-t from-pink-500 to-pink-400 rounded-t-lg"
                    style="height: 45%"
                  ></div>
                  <span class="text-white/50 text-xs">Mai</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                  <div
                    class="w-8 bg-gradient-to-t from-indigo-500 to-indigo-400 rounded-t-lg"
                    style="height: 85%"
                  ></div>
                  <span class="text-white/50 text-xs">Jun</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Titre et descriptions en pied de page -->
      <div class="absolute bottom-0 left-0 right-0 z-30">
        <div
          class="bg-gradient-to-r from-indigo-900/40 via-purple-900/30 to-blue-900/40 backdrop-blur-md border-t border-gradient-to-r border-indigo-400/20 py-8 px-8 shadow-lg"
        >
          <div class="text-left space-y-3">
            <!-- Titre principal avec couleurs - aligné à gauche -->
            <h1 class="text-2xl font-bold leading-tight">
              <span
                class="text-transparent bg-clip-text bg-gradient-to-r from-blue-200 via-white to-purple-200"
              >
                Gestion RH
              </span>
              <span
                class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-200 via-blue-200 to-indigo-200 ml-2"
              >
                Simplifiée
              </span>
            </h1>

            <!-- Conteneur pour les textes qui défilent - en dessous du titre -->
            <div class="h-6 overflow-hidden">
              <div class="sliding-texts">
                <p
                  class="text-white/90 text-sm font-medium leading-relaxed typing-animation"
                >
                  Une plateforme moderne pour optimiser la gestion de vos
                  ressources humaines
                </p>
                <p
                  class="text-white/90 text-sm font-medium leading-relaxed typing-animation"
                >
                  Simplifiez la gestion des congés et des plannings de vos
                  équipes
                </p>
                <p
                  class="text-white/90 text-sm font-medium leading-relaxed typing-animation"
                >
                  Automatisez vos processus RH avec notre intelligence
                  artificielle
                </p>
                <p
                  class="text-white/90 text-sm font-medium leading-relaxed typing-animation"
                >
                  Analysez les performances en temps réel avec nos tableaux de
                  bord
                </p>
                <p
                  class="text-white/90 text-sm font-medium leading-relaxed typing-animation"
                >
                  Centralisez tous vos documents et contrats en toute sécurité
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cartes flottantes colorées -->
      <div class="absolute inset-0 z-20 pointer-events-none">
        <!-- Card 1 - Gestion employés (Bleu) -->
        <div
          class="absolute top-12 right-12 animate-slide-in-right pointer-events-auto"
        >
          <div
            class="bg-gradient-to-br from-blue-500/20 to-blue-600/30 backdrop-blur-md rounded-xl p-4 border border-blue-300/30 shadow-lg transition-all duration-300 hover:from-blue-400/30 hover:to-blue-500/40 hover:-translate-y-1 hover:shadow-blue-500/20 hover:shadow-xl"
          >
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 bg-blue-400/30 rounded-lg flex items-center justify-center border border-blue-300/40"
              >
                <svg
                  class="w-4 h-4 text-blue-100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <span class="text-blue-100 text-sm font-medium"
                >Gestion employés</span
              >
            </div>
          </div>
        </div>

        <!-- Card 2 - Analytics (Violet) -->
        <div
          class="absolute bottom-32 right-16 animate-slide-in-right delay-300 pointer-events-auto"
        >
          <div
            class="bg-gradient-to-br from-purple-500/20 to-purple-600/30 backdrop-blur-md rounded-xl p-4 border border-purple-300/30 shadow-lg transition-all duration-300 hover:from-purple-400/30 hover:to-purple-500/40 hover:-translate-y-1 hover:shadow-purple-500/20 hover:shadow-xl"
          >
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 bg-purple-400/30 rounded-lg flex items-center justify-center border border-purple-300/40"
              >
                <svg
                  class="w-4 h-4 text-purple-100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <span class="text-purple-100 text-sm font-medium"
                >Analytics RH</span
              >
            </div>
          </div>
        </div>

        <!-- Card 3 - Automatisation (Vert/Cyan) -->
        <div
          class="absolute bottom-40 left-20 animate-slide-in-left delay-600 pointer-events-auto"
        >
          <div
            class="bg-gradient-to-br from-emerald-500/20 to-cyan-600/30 backdrop-blur-md rounded-xl p-4 border border-emerald-300/30 shadow-lg transition-all duration-300 hover:from-emerald-400/30 hover:to-cyan-500/40 hover:-translate-y-1 hover:shadow-emerald-500/20 hover:shadow-xl"
          >
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 bg-emerald-400/30 rounded-lg flex items-center justify-center border border-emerald-300/40"
              >
                <svg
                  class="w-4 h-4 text-emerald-100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <span class="text-emerald-100 text-sm font-medium"
                >Automatisation</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Section droite (formulaire d'authentification) -->
  <div
    class="flex flex-1 items-center justify-center bg-background animate-fade-in"
  >
    <div class="max-w-md min-w-max overflow-y-auto px-8 py-6 sm:w-[500px]">
      <!-- Logo pour mobile -->
      <div class="mb-8 flex justify-center lg:hidden">
        <img
          src="assets/logo-b.png"
          class="h-14 animate-pulse-subtle"
          alt="Logo LuminaHrGlobal"
        />
      </div>

      <!-- Conteneur pour router-outlet avec animation -->
      <div
        class="bg-white dark:bg-gray-900 rounded-xl shadow-xl p-8 border dark:border-gray-900 border-gray-100 animate-slide-up hover:shadow-2xl transition-all duration-500"
      >
        <router-outlet></router-outlet>
      </div>

      <!-- Footer -->
      <div
        class="mt-8 text-center text-xs text-gray-500 animate-fade-in delay-500"
      >
        <p>© 2025 LuminaHrGlobal. Tous droits réservés.</p>
        <div class="flex justify-center mt-3 space-x-4">
          <a
            href="#"
            class="text-gray-400 hover:text-gray-600 transition-colors hover:scale-105 transform duration-300"
            >Mentions légales</a
          >
          <span class="text-gray-300">•</span>
          <a
            href="#"
            class="text-gray-400 hover:text-gray-600 transition-colors hover:scale-105 transform duration-300"
            >Confidentialité</a
          >
          <span class="text-gray-300">•</span>
          <a
            href="#"
            class="text-gray-400 hover:text-gray-600 transition-colors hover:scale-105 transform duration-300"
            >Support</a
          >
        </div>
      </div>
    </div>
  </div>
</div>
