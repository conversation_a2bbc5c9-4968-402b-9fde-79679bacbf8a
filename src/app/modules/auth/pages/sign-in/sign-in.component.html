<!-- login form -->
<form class="my-10 space-y-6" [formGroup]="form" (ngSubmit)="onSubmit()">
  <div class="text-left">
    <h2 class="mb-1 text-3xl font-semibold text-foreground">
      Bonjour à nouveau <span class="text-primary">!</span>
    </h2>
    <p class="text-sm text-muted-foreground">
      Entrez vos identifiants pour accéder à votre compte.
    </p>
  </div>

  <div routerLink="/dashboard">
    <app-button full impact="bold" tone="light" shape="rounded" size="medium">
      <svg-icon src="assets/icons/google-logo.svg" [svgClass]="'h-6 w-6 mr-2'">
      </svg-icon>
      Se connecter avec Google
    </app-button>
  </div>

  <div
    class="my-4 flex items-center before:mt-0.5 before:flex-1 before:border-t before:border-muted after:mt-0.5 after:flex-1 after:border-t after:border-muted"
  >
    <p class="mx-4 mb-0 text-center text-sm text-muted-foreground">ou</p>
  </div>

  <div class="space-y-3 text-left">
    <div class="form__group">
      <div class="relative">
        <input
          type="text"
          id="email"
          [ngClass]="{ 'is__invalid-input': submitted && f['email'].errors }"
          class="peer block"
          placeholder=" "
          formControlName="email"
        />
        <label
          for="email"
          class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >
          Adresse e-mail
        </label>
      </div>
      <div *ngIf="submitted && f['email'].errors" class="is__invalid-error">
        <div *ngIf="f['email'].errors['required']">Champ requis</div>
        <div *ngIf="f['email'].errors['email']">
          L'adresse e-mail doit être valide
        </div>
      </div>
    </div>

    <div class="form__group">
      <div class="relative">
        <input
          [type]="passwordTextType ? 'text' : 'password'"
          id="password"
          [ngClass]="{ 'is__invalid-input': submitted && f['email'].errors }"
          class="peer block"
          placeholder=" "
          formControlName="password"
        />
        <label
          for="password"
          class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >
          Password
        </label>
        <span
          class="absolute top-2.5 right-5 cursor-pointer text-muted-foreground"
          (click)="togglePasswordTextType()"
        >
          <svg-icon
            [src]="
              !passwordTextType
                ? 'assets/icons/heroicons/outline/eye-off.svg'
                : 'assets/icons/heroicons/outline/eye.svg'
            "
            [svgClass]="'h-5 w-5'"
          >
          </svg-icon>
        </span>
      </div>
      <div *ngIf="submitted && f['password'].errors" class="is__invalid-error">
        <div *ngIf="f['password'].errors['required']">Champ requis</div>
      </div>
    </div>
  </div>

  <div class="mb-2 flex items-center justify-between space-x-3">
    <div class="flex items-center">
      <input id="remember-me" name="remember-me" type="checkbox" />
      <label for="remember-me" class="ml-2 block text-sm text-muted-foreground">
        Se souvenir de moi
      </label>
    </div>

    <app-button
      routerLink="/auth/forgot-password"
      impact="none"
      tone="primary"
      shape="rounded"
      size="small"
    >
      Mot de passe oublié ?
    </app-button>
  </div>
  <div *ngIf="errorMessage" class="text-red-500 text-sm mt-2">
    {{ errorMessage }}
  </div>
  <!-- Submit Button -->
  <div>
    <app-button full impact="bold" tone="primary" shape="rounded" size="medium">
      <span *ngIf="!isLoading">Sign in</span>
      <span *ngIf="isLoading">Loading...</span>
    </app-button>
  </div>

  <!-- Sign-up -->
  <div class="flex items-center text-sm text-muted-foreground">
    Pas encore membre ?
    <app-button
      routerLink="/auth/sign-up"
      impact="none"
      tone="primary"
      shape="rounded"
      size="small"
    >
      S'inscrire
    </app-button>
  </div>
</form>
