import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON> } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
  FormArray,
  AbstractControl,
  FormControl,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { CompanyService } from 'src/app/core/services/company/company.service';
import {
  updateForm,
  updateStep,
} from 'src/app/core/store/company/company-form.actions';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { ButtonComponent } from 'src/app/shared/components/button/button.component';

@Component({
  selector: 'app-create-company',
  templateUrl: './create-company.component.html',
  styleUrl: './create-company.component.scss',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>or,
    ToastrModule,
    ButtonComponent,
  ],
})
export class CreateCompanyComponent implements OnInit {
  form!: FormGroup;
  currentStep$ = this.store.select((state) => state.companyForm.currentStep);
  form$ = this.store.select((state) => state.companyForm.form);
  steps = [
    { label: 'Informations', completed: false },
    { label: 'Adresse', completed: false },
    { label: 'Fiscalité', completed: false },
    { label: 'Paie', completed: false },
    { label: 'Compléments', completed: false },
    { label: 'Confirmation', completed: false },
  ];
  isLoading = false;
  errorMessage: string | null = null;
  submitted = false;
  currentStep!: number;
  termsAgreed = false;
  logoPreview: string | ArrayBuffer | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private store: Store<AppState>,
    private companyService: CompanyService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.subscribeToStore();
  }

  private initForm(): void {
    this.form = this.fb.group({
      // Étape 1 : Informations de base
      companyName: ['', [Validators.required, Validators.maxLength(100)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumbers: this.fb.array([this.fb.control('', Validators.required)]),
      website: ['', [Validators.required, Validators.pattern('https?://.+')]],
      logo: [''],

      // Étape 2 : Adresse
      address: this.fb.group({
        street: ['', Validators.required],
        city: ['', Validators.required],
        postalCode: ['', Validators.required],
        country: ['', Validators.required],
      }),

      // Étape 3 : Paramètres fiscaux
      taxSettings: this.fb.group({
        incomeTaxRate: [
          0,
          [Validators.required, Validators.min(0), Validators.max(100)],
        ],
        socialSecurityRate: [
          0,
          [Validators.required, Validators.min(0), Validators.max(100)],
        ],
        unEmploymentInsuranceRate: [
          0,
          [Validators.required, Validators.min(0), Validators.max(100)],
        ],
        healthInsuranceRate: [
          0,
          [Validators.required, Validators.min(0), Validators.max(100)],
        ],
        pensionContributionRate: [
          0,
          [Validators.required, Validators.min(0), Validators.max(100)],
        ],
        incomeTaxThreshold: [null, Validators.min(0)],
        socialSecurityThreshold: [null, Validators.min(0)],
        taxPaymentFrequency: ['MONTHLY'],
      }),

      // Étape 4 : Configuration paie
      payrollConfiguration: this.fb.group({
        payrollCycle: ['MONTHLY', Validators.required],
        paymentDay: [
          1,
          [Validators.required, Validators.min(1), Validators.max(31)],
        ],
        overtimeMultiplier: [1.5, [Validators.required, Validators.min(1)]],
        maxOvertimeHours: [null, Validators.min(0)],
        bonusType: ['PERCENTAGE_OF_SALARY'],
        performanceBonusRate: [null, [Validators.min(0), Validators.max(100)]],
      }),

      // Étape 5 : Informations supplémentaires
      officialName: ['', Validators.required],
      taxIdentificationNumber: [
        '',
        [Validators.required, Validators.pattern(/^[A-Za-z0-9]+$/)],
      ],
      industry: ['', Validators.required],
      description: ['', Validators.maxLength(500)],
    });
  }

  onFileChange(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        this.logoPreview = reader.result;
        this.form.patchValue({ logo: reader.result });
      };
      reader.readAsDataURL(file);
    }
  }

  getPayrollCycleLabel(cycle: string): string {
    switch (cycle) {
      case 'MONTHLY':
        return 'Mensuel';
      case 'SEMI_MONTHLY':
        return 'Semi-mensuel';
      case 'BI_WEEKLY':
        return 'Bi-hebdomadaire';
      case 'WEEKLY':
        return 'Hebdomadaire';
      default:
        return cycle;
    }
  }

  private subscribeToStore(): void {
    this.form$.subscribe((form) =>
      this.form.patchValue(form, { emitEvent: false })
    );
    this.currentStep$.subscribe((step) => {
      this.currentStep = step;
      this.updateStepsCompletion();
    });
  }

  get phoneNumbers(): FormArray<FormControl<string>> {
    return this.form.get('phoneNumbers') as FormArray<FormControl<string>>;
  }

  addPhoneNumber(): void {
    this.phoneNumbers.push(
      new FormControl<string>('', {
        nonNullable: true,
        validators: Validators.required,
      })
    );
  }

  removePhoneNumber(index: number): void {
    if (this.phoneNumbers.length > 1) {
      this.phoneNumbers.removeAt(index);
    }
  }

  getFormGroup(name: string): FormGroup {
    return this.form.get(name) as FormGroup;
  }

  getControl(name: string | string[]): AbstractControl {
    if (Array.isArray(name)) {
      return this.form.get(name) as AbstractControl;
    }
    return this.form.get(name) as AbstractControl;
  }

  nextStep(): void {
    if (this.isCurrentStepValid()) {
      this.store.dispatch(updateForm({ form: this.form.value }));
      this.store.dispatch(updateStep({ step: this.currentStep + 1 }));
      this.markCurrentStepAsComplete();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      this.markAllAsTouched();
      this.toastr.error('Veuillez corriger les erreurs avant de continuer');
    }
  }

  prevStep(): void {
    this.store.dispatch(updateStep({ step: this.currentStep - 1 }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  goToStep(step: number): void {
    if (step <= this.currentStep) {
      this.store.dispatch(updateStep({ step }));
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  private isCurrentStepValid(): boolean | undefined {
    switch (this.currentStep) {
      case 1:
        return this.isStep1Valid();
      case 2:
        return this.isStep2Valid();
      case 3:
        return this.isStep3Valid();
      case 4:
        return this.isStep4Valid();
      case 5:
        return this.isStep5Valid();
      default:
        return true;
    }
  }

  private isStep1Valid(): boolean | undefined {
    return (
      this.form.get('companyName')?.valid &&
      this.form.get('email')?.valid &&
      this.phoneNumbers.valid &&
      this.form.get('website')?.valid
    );
  }

  private isStep2Valid(): boolean {
    return this.getFormGroup('address').valid;
  }

  private isStep3Valid(): boolean {
    return this.getFormGroup('taxSettings').valid;
  }

  private isStep4Valid(): boolean {
    return this.getFormGroup('payrollConfiguration').valid;
  }

  private isStep5Valid(): boolean | undefined {
    return (
      this.form.get('officialName')?.valid &&
      this.form.get('taxIdentificationNumber')?.valid &&
      this.form.get('industry')?.valid
    );
  }

  private markAllAsTouched(): void {
    this.submitted = true;
    Object.values(this.form.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach((c) => c.markAsTouched());
      }
    });
  }

  private markCurrentStepAsComplete(): void {
    this.steps[this.currentStep - 1].completed = true;
  }

  private updateStepsCompletion(): void {
    // Marquer toutes les étapes précédentes comme complétées
    for (let i = 0; i < this.currentStep - 1; i++) {
      this.steps[i].completed = true;
    }
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.form.invalid || !this.termsAgreed) {
      this.markAllAsTouched();
      this.toastr.error(
        'Veuillez accepter les conditions et corriger les erreurs'
      );
      return;
    }

    this.isLoading = true;
    const formData = this.prepareFormData();

    this.companyService.createCompany(formData).subscribe({
      next: () => {
        this.isLoading = false;
        this.toastr.success('Entreprise créée avec succès');
        this.router.navigate(['dashboard/overview']);
      },
      error: (err) => {
        this.isLoading = false;
        this.errorMessage = err.error?.message;
        this.toastr.error(this.errorMessage || 'Une erreur est survenue');
      },
    });
  }

  get f() {
    return this.form.controls;
  }

  get address() {
    return this.getFormGroup('address').controls;
  }

  get taxSettings() {
    return this.getFormGroup('taxSettings').controls;
  }

  get payrollConfig() {
    return this.getFormGroup('payrollConfiguration').controls;
  }

  private prepareFormData(): any {
    return {
      ...this.form.value,
      phoneNumbers: this.form.value.phoneNumbers.filter((num: string) => num),
    };
  }
}
