<!-- Style Twenty: Formulaire de création d'entreprise -->
<form class="space-y-6" [formGroup]="form">
  <!-- En-tête -->
  <div class="text-center">
    <h2 class="text-2xl font-bold text-black mb-2">
      Créez votre espace de travail
    </h2>
    <p class="text-black/70 text-sm">
      Un environnement partagé où vous pouvez collaborer avec votre équipe
    </p>
  </div>

  <!-- Indicateur d'étapes moderne -->
  <div class="mb-8">
    <!-- Barre de progression -->
    <div class="relative mb-6">
      <div class="h-1 bg-black/20 rounded-full">
        <div
          class="h-1 bg-primary rounded-full transition-all duration-500 ease-out"
          [style.width]="((currentStep - 1) / (steps.length - 1)) * 100 + '%'"
        ></div>
      </div>
    </div>

    <!-- Étapes -->
    <div class="flex justify-between items-center">
      <div
        *ngFor="let step of steps; let i = index"
        class="flex flex-col items-center cursor-pointer group"
        (click)="goToStep(i + 1)"
      >
        <div
          class="w-8 h-8 rounded-full border-2 flex items-center justify-center text-xs font-semibold transition-all duration-200"
          [ngClass]="{
            'bg-primary border-primary text-white':
              currentStep === i + 1 ||
              (step.completed && currentStep !== i + 1),
            'bg-black/10 border-black/30 text-black/60':
              !step.completed && currentStep !== i + 1
          }"
        >
          <i
            *ngIf="step.completed && currentStep !== i + 1"
            class="fas fa-check text-white"
          ></i>
          <span *ngIf="!(!step.completed && currentStep !== i + 1)">{{
            i + 1
          }}</span>
        </div>
        <span
          class="text-xs text-black/70 mt-2 font-medium group-hover:text-black transition-colors"
          >{{ step.label }}</span
        >
      </div>
    </div>
  </div>

  <!-- Étape 1 : Informations de base -->
  <div *ngIf="currentStep === 1" class="space-y-6">
    <!-- En-tête de l'étape -->
    <div class="text-center">
      <h3 class="text-xl font-semibold text-black mb-2">
        Informations de base
      </h3>
      <p class="text-black/70 text-sm">
        Commençons par les informations essentielles de votre entreprise
      </p>
    </div>

    <!-- Champs du formulaire -->
    <div class="space-y-6">
      <!-- Première ligne : Nom et Email -->
      <div class="grid grid-cols-2 gap-4">
        <!-- Nom de l'entreprise -->
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Nom de l'entreprise <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            formControlName="companyName"
            placeholder="Ex: Entreprise Lumina SARL"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
          <div
            *ngIf="submitted && form.get('companyName')?.invalid"
            class="mt-1 text-sm text-red-500"
          >
            <span *ngIf="form.get('companyName')?.errors?.['required']">
              Ce champ est obligatoire
            </span>
            <span *ngIf="form.get('companyName')?.errors?.['maxlength']">
              Maximum 100 caractères
            </span>
          </div>
        </div>

        <!-- Email professionnel -->
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Email professionnel <span class="text-red-500">*</span>
          </label>
          <input
            type="email"
            formControlName="email"
            placeholder="<EMAIL>"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
          <div
            *ngIf="submitted && form.get('email')?.invalid"
            class="mt-1 text-sm text-red-500"
          >
            <span *ngIf="form.get('email')?.errors?.['required']">
              Ce champ est obligatoire
            </span>
            <span *ngIf="form.get('email')?.errors?.['email']">
              Veuillez entrer une adresse email valide
            </span>
          </div>
        </div>
      </div>

      <!-- Deuxième ligne : Téléphone et Site web -->
      <div class="grid grid-cols-2 gap-4">
        <!-- Téléphone -->
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Numéro de téléphone <span class="text-red-500">*</span>
          </label>
          <div formArrayName="phoneNumbers">
            <div *ngFor="let phone of phoneNumbers.controls; let i = index">
              <input
                type="tel"
                [formControlName]="i"
                placeholder="+243 81 234 5678"
                class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
              />
              <div
                *ngIf="submitted && phone.invalid"
                class="mt-1 text-sm text-red-500"
              >
                Veuillez entrer un numéro de téléphone valide
              </div>
            </div>
          </div>
        </div>

        <!-- Site web -->
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Site web <span class="text-black/60">(optionnel)</span>
          </label>
          <input
            type="url"
            formControlName="website"
            placeholder="https://www.entreprise.cd"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
          <div
            *ngIf="submitted && form.get('website')?.invalid"
            class="mt-1 text-sm text-red-500"
          >
            Veuillez entrer une URL valide commençant par http:// ou https://
          </div>
        </div>
      </div>

      <!-- Logo -->
      <div>
        <label class="block text-sm font-medium text-black/80 mb-2">
          Logo de l'entreprise <span class="text-black/60">(optionnel)</span>
        </label>
        <input
          type="url"
          formControlName="logo"
          placeholder="https://exemple.com/logo.png ou lien vers votre logo"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
        />
        <p class="mt-1 text-xs text-black/60">
          Vous pouvez fournir un lien vers votre logo ou l'ajouter plus tard
          dans les paramètres
        </p>
      </div>

      <!-- Troisième ligne : Secteur et Nom officiel -->
      <div class="grid grid-cols-2 gap-4">
        <!-- Secteur d'activité -->
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Secteur d'activité <span class="text-black/60">(optionnel)</span>
          </label>
          <select
            formControlName="industry"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300 appearance-none cursor-pointer"
          >
            <option value="">Sélectionnez un secteur</option>
            <option value="Services">Services</option>
            <option value="Commerce">Commerce</option>
            <option value="Industrie">Industrie</option>
            <option value="Agriculture">Agriculture</option>
            <option value="Technologie">Technologie</option>
            <option value="Santé">Santé</option>
            <option value="Éducation">Éducation</option>
            <option value="Transport">Transport</option>
            <option value="Construction">Construction</option>
            <option value="Finance">Finance</option>
            <option value="Autre">Autre</option>
          </select>
        </div>

        <!-- Nom officiel -->
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Nom officiel
            <span class="text-black/60">(auto-rempli)</span>
          </label>
          <input
            type="text"
            formControlName="officialName"
            placeholder="Sera rempli automatiquement"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>
      </div>

      <!-- Numéro d'identification fiscale -->
      <div>
        <label class="block text-sm font-medium text-black/80 mb-2">
          Numéro d'identification fiscale
          <span class="text-black/60">(auto-généré si vide)</span>
        </label>
        <input
          type="text"
          formControlName="taxIdentificationNumber"
          placeholder="Sera généré automatiquement si non fourni"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
        />
      </div>

      <!-- Description -->
      <div>
        <label class="block text-sm font-medium text-black/80 mb-2">
          Description <span class="text-black/60">(optionnel)</span>
        </label>
        <textarea
          formControlName="description"
          placeholder="Décrivez brièvement votre entreprise..."
          rows="3"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300 resize-none"
        ></textarea>
      </div>
    </div>

    <!-- Bouton de navigation -->
    <div class="flex justify-end pt-6">
      <app-button
        (click)="nextStep()"
        impact="bold"
        tone="primary"
        shape="rounded"
        size="medium"
      >
        Continuer
      </app-button>
    </div>
  </div>

  <!-- Étape 2 : Adresse -->
  <div *ngIf="currentStep === 2" class="space-y-6">
    <!-- En-tête de l'étape -->
    <div class="text-center">
      <h3 class="text-xl font-semibold text-black mb-2">
        Adresse de l'entreprise
      </h3>
      <p class="text-black/70 text-sm">
        Indiquez l'adresse physique de votre entreprise
      </p>
    </div>

    <!-- Champs du formulaire -->
    <div formGroupName="address" class="space-y-4">
      <!-- Adresse complète -->
      <div>
        <label class="block text-sm font-medium text-black/80 mb-2">
          Adresse complète <span class="text-red-500">*</span>
        </label>
        <input
          type="text"
          formControlName="street"
          placeholder="Ex: 123 Avenue de la Libération, Gombe"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
        />
        <div
          *ngIf="submitted && form.get('address.street')?.invalid"
          class="mt-1 text-sm text-red-500"
        >
          Veuillez entrer une adresse valide
        </div>
      </div>

      <!-- Ville et Code postal -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Ville <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            formControlName="city"
            placeholder="Ex: Kinshasa"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
          <div
            *ngIf="submitted && form.get('address.city')?.invalid"
            class="mt-1 text-sm text-red-500"
          >
            Veuillez entrer une ville
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Code postal <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            formControlName="postalCode"
            placeholder="Ex: 8200"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
          <div
            *ngIf="submitted && form.get('address.postalCode')?.invalid"
            class="mt-1 text-sm text-red-500"
          >
            Veuillez entrer un code postal valide
          </div>
        </div>
      </div>

      <!-- Pays -->
      <div>
        <label class="block text-sm font-medium text-black/80 mb-2">
          Pays <span class="text-red-500">*</span>
        </label>
        <select
          formControlName="country"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300 appearance-none cursor-pointer"
        >
          <option value="" disabled class="text-gray-900">
            Sélectionnez un pays
          </option>
          <option
            value="République Démocratique du Congo"
            class="text-gray-900"
          >
            République Démocratique du Congo
          </option>
          <option value="Autre" class="text-gray-900">Autre</option>
        </select>
        <div
          *ngIf="submitted && form.get('address.country')?.invalid"
          class="mt-1 text-sm text-red-500"
        >
          Veuillez sélectionner un pays
        </div>
      </div>
    </div>

    <!-- Boutons de navigation -->
    <div class="flex justify-between pt-6">
      <app-button
        (click)="prevStep()"
        impact="light"
        tone="light"
        shape="rounded"
        size="medium"
      >
        Précédent
      </app-button>
      <app-button
        (click)="nextStep()"
        impact="bold"
        tone="primary"
        shape="rounded"
        size="medium"
      >
        Continuer
      </app-button>
    </div>
  </div>

  <!-- Étape 3 : Paramètres fiscaux -->
  <div *ngIf="currentStep === 3" class="space-y-6">
    <!-- En-tête de l'étape -->
    <div class="text-center">
      <h3 class="text-xl font-semibold text-black mb-2">Paramètres fiscaux</h3>
      <p class="text-black/70 text-sm">
        Configuration des taux d'imposition (valeurs par défaut pour la RDC)
      </p>
    </div>

    <!-- Champs du formulaire -->
    <div formGroupName="taxSettings" class="space-y-4">
      <!-- Taux d'impôt sur le revenu -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Taux d'impôt sur le revenu (%)
          </label>
          <input
            type="number"
            formControlName="incomeTaxRate"
            placeholder="30"
            min="0"
            max="100"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Taux CNSS (%)
          </label>
          <input
            type="number"
            formControlName="socialSecurityRate"
            placeholder="3.5"
            min="0"
            max="100"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>
      </div>

      <!-- Taux pension et fréquence -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Taux pension CNSS (%)
          </label>
          <input
            type="number"
            formControlName="pensionContributionRate"
            placeholder="3.5"
            min="0"
            max="100"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Fréquence de paiement
          </label>
          <select
            formControlName="taxPaymentFrequency"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300 appearance-none cursor-pointer"
          >
            <option value="MONTHLY">Mensuel</option>
            <option value="QUARTERLY">Trimestriel</option>
            <option value="YEARLY">Annuel</option>
          </select>
        </div>
      </div>

      <!-- Seuils d'imposition -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Seuil d'impôt sur le revenu (CDF)
          </label>
          <input
            type="number"
            formControlName="incomeTaxThreshold"
            placeholder="0"
            min="0"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Seuil CNSS (CDF)
          </label>
          <input
            type="number"
            formControlName="socialSecurityThreshold"
            placeholder="0"
            min="0"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>
      </div>

      <!-- Taux assurance chômage et santé -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Taux assurance chômage (%)
          </label>
          <input
            type="number"
            formControlName="unEmploymentInsuranceRate"
            placeholder="0"
            min="0"
            max="100"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Taux assurance santé (%)
          </label>
          <input
            type="number"
            formControlName="healthInsuranceRate"
            placeholder="0"
            min="0"
            max="100"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>
      </div>
    </div>

    <!-- Boutons de navigation -->
    <div class="flex justify-between pt-6">
      <app-button
        (click)="prevStep()"
        impact="light"
        tone="light"
        shape="rounded"
        size="medium"
      >
        Précédent
      </app-button>
      <app-button
        (click)="nextStep()"
        impact="bold"
        tone="primary"
        shape="rounded"
        size="medium"
      >
        Continuer
      </app-button>
    </div>
  </div>

  <!-- Étape 4 : Configuration paie -->
  <div *ngIf="currentStep === 4" class="space-y-6">
    <!-- En-tête de l'étape -->
    <div class="text-center">
      <h3 class="text-xl font-semibold text-black mb-2">Configuration paie</h3>
      <p class="text-black/70 text-sm">
        Paramètres de gestion de la paie et des heures supplémentaires
      </p>
    </div>

    <!-- Champs du formulaire -->
    <div formGroupName="payrollConfiguration" class="space-y-4">
      <!-- Cycle de paie et jour de paiement -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Cycle de paie
          </label>
          <select
            formControlName="payrollCycle"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300 appearance-none cursor-pointer"
          >
            <option value="WEEKLY">Hebdomadaire</option>
            <option value="BIWEEKLY">Bi-hebdomadaire</option>
            <option value="MONTHLY">Mensuel</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Jour de paiement
          </label>
          <input
            type="number"
            formControlName="paymentDay"
            placeholder="30"
            min="1"
            max="31"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>
      </div>

      <!-- Heures supplémentaires -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Multiplicateur heures sup.
          </label>
          <input
            type="number"
            formControlName="overtimeMultiplier"
            placeholder="1.5"
            min="1"
            step="0.1"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Max heures sup./mois
          </label>
          <input
            type="number"
            formControlName="maxOvertimeHours"
            placeholder="40"
            min="0"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>
      </div>

      <!-- Configuration des bonus -->
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Type de bonus
          </label>
          <select
            formControlName="bonusType"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300 appearance-none cursor-pointer"
          >
            <option value="PERCENTAGE_OF_SALARY">Pourcentage du salaire</option>
            <option value="FIXED_AMOUNT">Montant fixe</option>
            <option value="PERFORMANCE_BASED">Basé sur la performance</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-black/80 mb-2">
            Taux bonus performance (%)
          </label>
          <input
            type="number"
            formControlName="performanceBonusRate"
            placeholder="0"
            min="0"
            max="100"
            class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
          />
        </div>
      </div>
    </div>

    <!-- Boutons de navigation -->
    <div class="flex justify-between pt-6">
      <app-button
        (click)="prevStep()"
        impact="light"
        tone="light"
        shape="rounded"
        size="medium"
      >
        Précédent
      </app-button>
      <app-button
        (click)="nextStep()"
        impact="bold"
        tone="primary"
        shape="rounded"
        size="medium"
      >
        Continuer
      </app-button>
    </div>
  </div>

  <!-- Étape finale : Confirmation -->
  <div *ngIf="currentStep === steps.length" class="space-y-6">
    <!-- En-tête de l'étape -->
    <div class="text-center">
      <h3 class="text-xl font-semibold text-black mb-2">Finalisation</h3>
      <p class="text-black/70 text-sm">
        Votre espace de travail est prêt à être créé
      </p>
    </div>

    <!-- Résumé -->
    <div class="bg-black/5 rounded-xl p-6 space-y-4 border border-black/20">
      <div class="grid grid-cols-2 gap-4">
        <!-- Informations de base -->
        <div class="space-y-3">
          <h4 class="font-semibold text-black">Informations de base</h4>
          <div>
            <span class="text-black/60 text-sm">Nom de l'entreprise:</span>
            <div class="text-black font-medium">
              {{ form.get("companyName")?.value }}
            </div>
          </div>
          <div>
            <span class="text-black/60 text-sm">Email:</span>
            <div class="text-black font-medium">
              {{ form.get("email")?.value }}
            </div>
          </div>
          <div *ngIf="form.get('website')?.value">
            <span class="text-black/60 text-sm">Site web:</span>
            <div class="text-black font-medium">
              {{ form.get("website")?.value }}
            </div>
          </div>
          <div *ngIf="form.get('industry')?.value">
            <span class="text-black/60 text-sm">Secteur:</span>
            <div class="text-black font-medium">
              {{ form.get("industry")?.value }}
            </div>
          </div>
        </div>

        <!-- Adresse -->
        <div class="space-y-3">
          <h4 class="font-semibold text-black">Adresse</h4>
          <div *ngIf="form.get('address.street')?.value">
            <span class="text-black/60 text-sm">Adresse:</span>
            <div class="text-black font-medium">
              {{ form.get("address.street")?.value }},
              {{ form.get("address.city")?.value }}
              {{ form.get("address.postalCode")?.value }},
              {{ form.get("address.country")?.value }}
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4 pt-4 border-t border-black/10">
        <!-- Paramètres fiscaux -->
        <div class="space-y-3">
          <h4 class="font-semibold text-black">Paramètres fiscaux</h4>
          <div>
            <span class="text-black/60 text-sm">Impôt sur le revenu:</span>
            <div class="text-black font-medium">
              {{ form.get("taxSettings.incomeTaxRate")?.value }}%
            </div>
          </div>
          <div>
            <span class="text-black/60 text-sm">CNSS:</span>
            <div class="text-black font-medium">
              {{ form.get("taxSettings.socialSecurityRate")?.value }}%
            </div>
          </div>
          <div>
            <span class="text-black/60 text-sm">Fréquence:</span>
            <div class="text-black font-medium">
              {{ form.get("taxSettings.taxPaymentFrequency")?.value }}
            </div>
          </div>
        </div>

        <!-- Configuration paie -->
        <div class="space-y-3">
          <h4 class="font-semibold text-black">Configuration paie</h4>
          <div>
            <span class="text-black/60 text-sm">Cycle de paie:</span>
            <div class="text-black font-medium">
              {{ form.get("payrollConfiguration.payrollCycle")?.value }}
            </div>
          </div>
          <div>
            <span class="text-black/60 text-sm">Jour de paiement:</span>
            <div class="text-black font-medium">
              {{ form.get("payrollConfiguration.paymentDay")?.value }}
            </div>
          </div>
          <div>
            <span class="text-black/60 text-sm">Heures sup.:</span>
            <div class="text-black font-medium">
              {{ form.get("payrollConfiguration.overtimeMultiplier")?.value }}x
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Boutons de navigation -->
    <div class="flex justify-between pt-6">
      <app-button
        (click)="prevStep()"
        impact="light"
        tone="light"
        shape="rounded"
        size="medium"
      >
        Précédent
      </app-button>
      <app-button
        (click)="onSubmit()"
        impact="bold"
        tone="primary"
        shape="rounded"
        size="medium"
        type="submit"
      >
        <span *ngIf="!isLoading">Créer l'espace de travail</span>
        <span *ngIf="isLoading" class="flex items-center">
          <svg
            class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Création...
        </span>
      </app-button>
    </div>
  </div>
</form>
