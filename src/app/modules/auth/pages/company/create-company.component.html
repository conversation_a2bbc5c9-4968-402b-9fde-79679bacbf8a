<!-- Style Twenty: Création d'entreprise avec fond d'image -->
<div class="min-h-screen w-screen relative overflow-hidden font-inter">
  <!-- Image de fond de la plateforme avec effet flou -->
  <div class="absolute inset-0 z-0">
    <div
      class="absolute inset-0 bg-cover bg-center bg-no-repeat"
      style="background-image: url('assets/images/auth-screens-2.png')"
    ></div>
    <!-- Overlay sombre pour améliorer la lisibilité -->
    <div class="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
  </div>

  <!-- Logo en haut à gauche -->
  <div class="absolute top-6 left-6 z-30">
    <img
      src="assets/logo-w.png"
      class="h-8 transition-transform duration-300 hover:scale-105"
      alt="Logo LuminaHrGlobal"
    />
  </div>

  <!-- Contenu principal centré -->
  <div class="relative z-20 min-h-screen flex items-center justify-center p-6">
    <div class="w-full max-w-2xl">
      <!-- En-tête -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-white mb-3">
          Créez votre espace de travail
        </h1>
        <p class="text-white/70 max-w-md mx-auto">
          Un environnement partagé où vous pouvez collaborer avec votre équipe
        </p>
      </div>

      <!-- Indicateur d'étapes moderne -->
      <div class="mb-8">
        <!-- Barre de progression -->
        <div class="relative mb-6">
          <div class="h-1 bg-white/20 rounded-full">
            <div
              class="h-1 bg-white rounded-full transition-all duration-500 ease-out"
              [style.width]="
                ((currentStep - 1) / (steps.length - 1)) * 100 + '%'
              "
            ></div>
          </div>
        </div>

        <!-- Étapes -->
        <div class="flex justify-between items-center">
          <div
            *ngFor="let step of steps; let i = index"
            class="flex flex-col items-center cursor-pointer group"
            (click)="goToStep(i + 1)"
          >
            <div
              class="w-8 h-8 rounded-full border-2 flex items-center justify-center text-xs font-semibold transition-all duration-200"
              [ngClass]="{
                'bg-white border-white text-gray-900':
                  currentStep === i + 1 ||
                  (step.completed && currentStep !== i + 1),
                'bg-white/10 border-white/30 text-white/60':
                  !step.completed && currentStep !== i + 1
              }"
            >
              <i
                *ngIf="step.completed && currentStep !== i + 1"
                class="fas fa-check"
              ></i>
              <span *ngIf="!(step.completed && currentStep !== i + 1)">{{
                i + 1
              }}</span>
            </div>
            <span
              class="text-xs text-white/70 mt-2 font-medium group-hover:text-white transition-colors"
              >{{ step.label }}</span
            >
          </div>
        </div>
      </div>

      <!-- Conteneur du formulaire avec glassmorphism -->
      <div
        class="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl p-8"
      >
        <form [formGroup]="form">
          <!-- Étape 1 : Informations de base -->
          <div *ngIf="currentStep === 1" class="space-y-6">
            <!-- En-tête de l'étape -->
            <div class="text-center">
              <h2 class="text-xl font-semibold text-white mb-2">
                Informations de base
              </h2>
              <p class="text-white/70 text-sm">
                Commençons par les informations essentielles de votre entreprise
              </p>
            </div>

            <!-- Champs du formulaire -->
            <div class="space-y-4">
              <!-- Nom de l'entreprise -->
              <div>
                <label class="block text-sm font-medium text-white/80 mb-2">
                  Nom de l'entreprise <span class="text-red-300">*</span>
                </label>
                <input
                  type="text"
                  formControlName="companyName"
                  placeholder="Ex: Entreprise Lumina SARL"
                  class="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300"
                />
                <div
                  *ngIf="submitted && form.get('companyName')?.invalid"
                  class="mt-1 text-sm text-red-300"
                >
                  <span *ngIf="form.get('companyName')?.errors?.['required']">
                    Ce champ est obligatoire
                  </span>
                  <span *ngIf="form.get('companyName')?.errors?.['maxlength']">
                    Maximum 100 caractères
                  </span>
                </div>
              </div>

              <!-- Email professionnel -->
              <div>
                <label class="block text-sm font-medium text-white/80 mb-2">
                  Email professionnel <span class="text-red-300">*</span>
                </label>
                <input
                  type="email"
                  formControlName="email"
                  placeholder="<EMAIL>"
                  class="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300"
                />
                <div
                  *ngIf="submitted && form.get('email')?.invalid"
                  class="mt-1 text-sm text-red-300"
                >
                  <span *ngIf="form.get('email')?.errors?.['required']">
                    Ce champ est obligatoire
                  </span>
                  <span *ngIf="form.get('email')?.errors?.['email']">
                    Veuillez entrer une adresse email valide
                  </span>
                </div>
              </div>

              <!-- Téléphone -->
              <div>
                <label class="block text-sm font-medium text-white/80 mb-2">
                  Numéro de téléphone <span class="text-red-300">*</span>
                </label>
                <div formArrayName="phoneNumbers">
                  <div
                    *ngFor="let phone of phoneNumbers.controls; let i = index"
                  >
                    <input
                      type="tel"
                      [formControlName]="i"
                      placeholder="+243 81 234 5678"
                      class="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300"
                    />
                    <div
                      *ngIf="submitted && phone.invalid"
                      class="mt-1 text-sm text-red-300"
                    >
                      Veuillez entrer un numéro de téléphone valide
                    </div>
                  </div>
                </div>
              </div>

              <!-- Site web -->
              <div>
                <label class="block text-sm font-medium text-white/80 mb-2">
                  Site web <span class="text-white/60">(optionnel)</span>
                </label>
                <input
                  type="url"
                  formControlName="website"
                  placeholder="https://www.entreprise.cd"
                  class="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300"
                />
                <div
                  *ngIf="submitted && form.get('website')?.invalid"
                  class="mt-1 text-sm text-red-300"
                >
                  Veuillez entrer une URL valide commençant par http:// ou
                  https://
                </div>
              </div>
            </div>

            <!-- Bouton de navigation -->
            <div class="flex justify-end pt-6">
              <button
                type="button"
                (click)="nextStep()"
                class="px-8 py-3 bg-white text-gray-900 font-semibold rounded-xl hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-white/30 transition-all duration-300"
              >
                Continuer
              </button>
            </div>
          </div>

          <!-- Étape 2 : Adresse -->
          <div *ngIf="currentStep === 2" class="space-y-6">
            <!-- En-tête de l'étape -->
            <div class="text-center">
              <h2 class="text-xl font-semibold text-white mb-2">
                Adresse de l'entreprise
              </h2>
              <p class="text-white/70 text-sm">
                Indiquez l'adresse physique de votre entreprise
              </p>
            </div>

            <!-- Champs du formulaire -->
            <div formGroupName="address" class="space-y-4">
              <!-- Adresse complète -->
              <div>
                <label class="block text-sm font-medium text-white/80 mb-2">
                  Adresse complète <span class="text-red-300">*</span>
                </label>
                <input
                  type="text"
                  formControlName="street"
                  placeholder="Ex: 123 Avenue de la Libération, Gombe"
                  class="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300"
                />
                <div
                  *ngIf="submitted && form.get('address.street')?.invalid"
                  class="mt-1 text-sm text-red-300"
                >
                  Veuillez entrer une adresse valide
                </div>
              </div>

              <!-- Ville et Code postal -->
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-white/80 mb-2">
                    Ville <span class="text-red-300">*</span>
                  </label>
                  <input
                    type="text"
                    formControlName="city"
                    placeholder="Ex: Kinshasa"
                    class="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300"
                  />
                  <div
                    *ngIf="submitted && form.get('address.city')?.invalid"
                    class="mt-1 text-sm text-red-300"
                  >
                    Veuillez entrer une ville
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-white/80 mb-2">
                    Code postal <span class="text-red-300">*</span>
                  </label>
                  <input
                    type="text"
                    formControlName="postalCode"
                    placeholder="Ex: 8200"
                    class="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300"
                  />
                  <div
                    *ngIf="submitted && form.get('address.postalCode')?.invalid"
                    class="mt-1 text-sm text-red-300"
                  >
                    Veuillez entrer un code postal valide
                  </div>
                </div>
              </div>

              <!-- Pays -->
              <div>
                <label class="block text-sm font-medium text-white/80 mb-2">
                  Pays <span class="text-red-300">*</span>
                </label>
                <select
                  formControlName="country"
                  class="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300 appearance-none cursor-pointer"
                >
                  <option value="" disabled class="text-gray-900">
                    Sélectionnez un pays
                  </option>
                  <option
                    value="République Démocratique du Congo"
                    class="text-gray-900"
                  >
                    République Démocratique du Congo
                  </option>
                  <option value="Autre" class="text-gray-900">Autre</option>
                </select>
                <div
                  *ngIf="submitted && form.get('address.country')?.invalid"
                  class="mt-1 text-sm text-red-300"
                >
                  Veuillez sélectionner un pays
                </div>
              </div>
            </div>

            <!-- Boutons de navigation -->
            <div class="flex justify-between pt-6">
              <button
                type="button"
                (click)="prevStep()"
                class="px-6 py-3 bg-white/20 backdrop-blur-sm border border-white/30 text-white font-medium rounded-xl hover:bg-white/30 transition-all duration-300"
              >
                Précédent
              </button>
              <button
                type="button"
                (click)="nextStep()"
                class="px-8 py-3 bg-white text-gray-900 font-semibold rounded-xl hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-white/30 transition-all duration-300"
              >
                Continuer
              </button>
            </div>
          </div>

          <!-- Étape finale : Confirmation -->
          <div *ngIf="currentStep === steps.length" class="space-y-6">
            <!-- En-tête de l'étape -->
            <div class="text-center">
              <h2 class="text-xl font-semibold text-white mb-2">
                Finalisation
              </h2>
              <p class="text-white/70 text-sm">
                Votre espace de travail est prêt à être créé
              </p>
            </div>

            <!-- Résumé -->
            <div class="bg-white/5 rounded-xl p-6 space-y-4">
              <div>
                <span class="text-white/60 text-sm">Nom de l'entreprise:</span>
                <div class="text-white font-medium">
                  {{ form.get("companyName")?.value }}
                </div>
              </div>
              <div>
                <span class="text-white/60 text-sm">Email:</span>
                <div class="text-white font-medium">
                  {{ form.get("email")?.value }}
                </div>
              </div>
              <div *ngIf="form.get('website')?.value">
                <span class="text-white/60 text-sm">Site web:</span>
                <div class="text-white font-medium">
                  {{ form.get("website")?.value }}
                </div>
              </div>
              <div *ngIf="form.get('address.street')?.value">
                <span class="text-white/60 text-sm">Adresse:</span>
                <div class="text-white font-medium">
                  {{ form.get("address.street")?.value }},
                  {{ form.get("address.city")?.value }}
                  {{ form.get("address.postalCode")?.value }},
                  {{ form.get("address.country")?.value }}
                </div>
              </div>
            </div>

            <!-- Boutons de navigation -->
            <div class="flex justify-between pt-6">
              <button
                type="button"
                (click)="prevStep()"
                class="px-6 py-3 bg-white/20 backdrop-blur-sm border border-white/30 text-white font-medium rounded-xl hover:bg-white/30 transition-all duration-300"
              >
                Précédent
              </button>
              <button
                type="submit"
                (click)="onSubmit()"
                [disabled]="isLoading"
                class="px-8 py-3 bg-white text-gray-900 font-semibold rounded-xl hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-white/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span *ngIf="!isLoading">Créer l'espace de travail</span>
                <span *ngIf="isLoading" class="flex items-center">
                  <svg
                    class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-900"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Création...
                </span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
