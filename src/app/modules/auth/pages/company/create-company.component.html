<!-- Container principal épuré -->
<div class="min-h-screen bg-gray-50">
  <div class="mx-auto max-w-3xl px-6 py-12">
    <!-- En-tête minimaliste -->
    <div class="text-center mb-12">
      <h1 class="text-3xl font-light text-gray-900 mb-3">
        Création d'entreprise
      </h1>
      <p class="text-gray-600 max-w-md mx-auto">
        Configurez votre entreprise en quelques étapes
      </p>
    </div>

    <!-- Indicateur d'étapes épuré -->
    <div class="mb-8">
      <!-- Barre de progression minimaliste -->
      <div class="relative mb-6">
        <div class="h-px bg-gray-300">
          <div
            class="h-px bg-gray-900 transition-all duration-500 ease-out"
            [style.width]="((currentStep - 1) / (steps.length - 1)) * 100 + '%'"
          ></div>
        </div>
      </div>

      <!-- <PERSON>tapes épurées -->
      <div class="flex justify-between items-center">
        <div
          *ngFor="let step of steps; let i = index"
          class="flex flex-col items-center cursor-pointer"
          (click)="goToStep(i + 1)"
        >
          <div
            class="w-7 h-7 rounded-full border flex items-center justify-center text-xs font-medium transition-all duration-200"
            [ngClass]="{
              'bg-gray-900 border-gray-900 text-white':
                currentStep === i + 1 ||
                (step.completed && currentStep !== i + 1),
              'bg-white border-gray-300 text-gray-500':
                !step.completed && currentStep !== i + 1
            }"
          >
            <i
              *ngIf="step.completed && currentStep !== i + 1"
              class="fas fa-check"
            ></i>
            <span *ngIf="!(step.completed && currentStep !== i + 1)">{{
              i + 1
            }}</span>
          </div>
          <span class="text-xs text-gray-500 mt-1.5 font-medium">{{
            step.label
          }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulaire épuré -->
  <div class="mx-auto max-w-2xl px-6 pb-12">
    <form [formGroup]="form">
      <!-- Étape 1 : Informations de base -->
      <div *ngIf="currentStep === 1" class="animate-fade-in">
        <!-- Carte épurée -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <!-- En-tête simple -->
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">
              Informations de base
            </h2>
          </div>

          <!-- Contenu du formulaire -->
          <div class="p-6">
            <div class="space-y-6">
              <!-- Champ : Nom de l'entreprise -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Nom de l'entreprise <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  formControlName="companyName"
                  placeholder="Ex: Entreprise Lumina SARL"
                  [ngClass]="{
                    'border-red-300 focus:border-red-500 focus:ring-red-500': submitted && form.get('companyName')?.invalid,
                    'border-gray-300 focus:border-gray-900 focus:ring-gray-900': !(submitted && form.get('companyName')?.invalid)
                  }"
                  class="block w-full px-3 py-2 border rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-1 transition-colors"
                />
                <div
                  *ngIf="submitted && form.get('companyName')?.invalid"
                  class="mt-1 text-sm text-red-600"
                >
                  <span *ngIf="form.get('companyName')?.errors?.['required']">
                    Ce champ est obligatoire
                  </span>
                  <span *ngIf="form.get('companyName')?.errors?.['maxlength']">
                    Maximum 100 caractères
                  </span>
                </div>
              </div>

                <!-- Champ : Email professionnel -->
                <div class="group">
                  <label
                    class="block text-sm font-bold text-gray-800 mb-3 tracking-wide"
                  >
                    Email professionnel
                    <span class="text-red-500 ml-1">*</span>
                  </label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                    >
                      <i
                        class="fas fa-envelope text-gray-400 group-focus-within:text-blue-500 transition-colors"
                      ></i>
                    </div>
                    <input
                      type="email"
                      formControlName="email"
                      placeholder="<EMAIL>"
                      [ngClass]="{
                        'border-red-400 bg-red-50 focus:border-red-500 focus:ring-red-500/20':
                          submitted && form.get('email')?.invalid,
                        'border-emerald-400 bg-emerald-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                          form.get('email')?.valid && form.get('email')?.dirty,
                        'border-gray-200 bg-gray-50 focus:border-blue-500 focus:ring-blue-500/20':
                          !(submitted && form.get('email')?.invalid) &&
                          !(
                            form.get('email')?.valid && form.get('email')?.dirty
                          )
                      }"
                      class="block w-full pl-12 pr-12 py-4 rounded-2xl border-2 font-medium text-gray-900 placeholder-gray-400 focus:ring-4 focus:outline-none transition-all duration-300 shadow-sm hover:shadow-md"
                    />
                    <!-- Icône de validation -->
                    <div
                      *ngIf="
                        form.get('email')?.valid && form.get('email')?.dirty
                      "
                      class="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <div
                        class="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center"
                      >
                        <i class="fas fa-check text-xs text-white"></i>
                      </div>
                    </div>
                    <!-- Icône d'erreur -->
                    <div
                      *ngIf="submitted && form.get('email')?.invalid"
                      class="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <div
                        class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
                      >
                        <i class="fas fa-times text-xs text-white"></i>
                      </div>
                    </div>
                  </div>
                  <!-- Messages d'erreur stylisés -->
                  <div
                    *ngIf="submitted && form.get('email')?.invalid"
                    class="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl"
                  >
                    <div class="flex items-center space-x-2">
                      <i
                        class="fas fa-exclamation-triangle text-red-500 text-sm"
                      ></i>
                      <span class="text-sm font-medium text-red-700">
                        {{ form.get('email')?.errors?.['required'] ? 'Ce champ est obligatoire' : 'Veuillez entrer une adresse email valide' }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Champ : Site web -->
                <div class="group">
                  <label
                    class="block text-sm font-bold text-gray-800 mb-3 tracking-wide"
                  >
                    Site web
                    <span class="text-red-500 ml-1">*</span>
                  </label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                    >
                      <i
                        class="fas fa-globe text-gray-400 group-focus-within:text-blue-500 transition-colors"
                      ></i>
                    </div>
                    <input
                      type="url"
                      formControlName="website"
                      placeholder="https://www.entreprise.cd"
                      [ngClass]="{
                        'border-red-400 bg-red-50 focus:border-red-500 focus:ring-red-500/20':
                          submitted && form.get('website')?.invalid,
                        'border-emerald-400 bg-emerald-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                          form.get('website')?.valid &&
                          form.get('website')?.dirty,
                        'border-gray-200 bg-gray-50 focus:border-blue-500 focus:ring-blue-500/20':
                          !(submitted && form.get('website')?.invalid) &&
                          !(
                            form.get('website')?.valid &&
                            form.get('website')?.dirty
                          )
                      }"
                      class="block w-full pl-12 pr-12 py-4 rounded-2xl border-2 font-medium text-gray-900 placeholder-gray-400 focus:ring-4 focus:outline-none transition-all duration-300 shadow-sm hover:shadow-md"
                    />
                    <!-- Icône de validation -->
                    <div
                      *ngIf="
                        form.get('website')?.valid && form.get('website')?.dirty
                      "
                      class="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <div
                        class="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center"
                      >
                        <i class="fas fa-check text-xs text-white"></i>
                      </div>
                    </div>
                    <!-- Icône d'erreur -->
                    <div
                      *ngIf="submitted && form.get('website')?.invalid"
                      class="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <div
                        class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
                      >
                        <i class="fas fa-times text-xs text-white"></i>
                      </div>
                    </div>
                  </div>
                  <!-- Messages d'erreur stylisés -->
                  <div
                    *ngIf="submitted && form.get('website')?.invalid"
                    class="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl"
                  >
                    <div class="flex items-center space-x-2">
                      <i
                        class="fas fa-exclamation-triangle text-red-500 text-sm"
                      ></i>
                      <span class="text-sm font-medium text-red-700">
                        Veuillez entrer une URL valide commençant par http:// ou
                        https://
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Colonne droite -->
              <div class="space-y-6">
                <!-- Champ : Numéros de téléphone -->
                <div class="group">
                  <label
                    class="block text-sm font-bold text-gray-800 mb-3 tracking-wide"
                  >
                    Numéros de téléphone
                    <span class="text-red-500 ml-1">*</span>
                  </label>
                  <div formArrayName="phoneNumbers" class="space-y-4">
                    <div
                      *ngFor="let phone of phoneNumbers.controls; let i = index"
                      class="flex items-start gap-3"
                    >
                      <div class="flex-1 relative group">
                        <div
                          class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                        >
                          <i
                            class="fas fa-phone text-gray-400 group-focus-within:text-blue-500 transition-colors"
                          ></i>
                        </div>
                        <input
                          type="tel"
                          [formControl]="phone"
                          placeholder="+243 81 234 5678"
                          [ngClass]="{
                            'border-red-400 bg-red-50 focus:border-red-500 focus:ring-red-500/20':
                              submitted && phone.invalid,
                            'border-emerald-400 bg-emerald-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                              phone.valid && phone.dirty,
                            'border-gray-200 bg-gray-50 focus:border-blue-500 focus:ring-blue-500/20':
                              !(submitted && phone.invalid) &&
                              !(phone.valid && phone.dirty)
                          }"
                          class="block w-full pl-12 pr-12 py-4 rounded-2xl border-2 font-medium text-gray-900 placeholder-gray-400 focus:ring-4 focus:outline-none transition-all duration-300 shadow-sm hover:shadow-md"
                        />
                        <!-- Icône de validation -->
                        <div
                          *ngIf="phone.valid && phone.dirty"
                          class="absolute inset-y-0 right-0 pr-4 flex items-center"
                        >
                          <div
                            class="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center"
                          >
                            <i class="fas fa-check text-xs text-white"></i>
                          </div>
                        </div>
                      </div>
                      <!-- Bouton de suppression stylisé -->
                      <button
                        type="button"
                        *ngIf="phoneNumbers.length > 1"
                        (click)="removePhoneNumber(i)"
                        class="mt-1 w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-4 focus:ring-red-500/20 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        <i class="fas fa-trash-alt text-sm"></i>
                      </button>
                    </div>
                  </div>
                  <!-- Bouton d'ajout stylisé -->
                  <button
                    type="button"
                    (click)="addPhoneNumber()"
                    class="mt-4 inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-xl hover:from-gray-200 hover:to-gray-300 focus:outline-none focus:ring-4 focus:ring-gray-500/20 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
                  >
                    <i class="fas fa-plus mr-2 text-sm"></i>
                    Ajouter un numéro
                  </button>
                </div>

                <!-- Champ : Logo de l'entreprise -->
                <div class="group">
                  <label
                    class="block text-sm font-bold text-gray-800 mb-3 tracking-wide"
                  >
                    Logo de l'entreprise
                    <span class="text-gray-500 text-xs font-normal ml-2"
                      >(optionnel)</span
                    >
                  </label>
                  <div class="flex items-center gap-6">
                    <!-- Zone de téléchargement de logo -->
                    <div class="relative">
                      <input
                        type="file"
                        id="logoUpload"
                        (change)="onFileChange($event)"
                        class="hidden"
                        accept="image/*"
                      />
                      <label for="logoUpload" class="cursor-pointer group">
                        <div
                          class="w-20 h-20 rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center overflow-hidden border-2 border-dashed border-gray-300 hover:border-blue-500 hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 shadow-sm hover:shadow-lg"
                        >
                          <div *ngIf="!logoPreview" class="text-center">
                            <i
                              class="fas fa-camera text-2xl text-gray-400 group-hover:text-blue-500 transition-colors mb-1"
                            ></i>
                            <div class="text-xs text-gray-500 font-medium">
                              Upload
                            </div>
                          </div>
                          <img
                            *ngIf="logoPreview"
                            [src]="logoPreview"
                            alt="Logo preview"
                            class="w-full h-full object-cover rounded-xl"
                          />
                        </div>
                      </label>
                    </div>

                    <!-- Champ URL alternative -->
                    <div class="flex-1 relative group">
                      <div
                        class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                      >
                        <i
                          class="fas fa-link text-gray-400 group-focus-within:text-blue-500 transition-colors"
                        ></i>
                      </div>
                      <input
                        type="text"
                        formControlName="logo"
                        placeholder="Ou entrez une URL vers votre logo"
                        class="block w-full pl-12 pr-4 py-4 rounded-2xl border-2 border-gray-200 bg-gray-50 font-medium text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 focus:outline-none transition-all duration-300 shadow-sm hover:shadow-md"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Boutons de navigation premium -->
          <div class="bg-gray-50/50 px-8 py-6 border-t border-gray-100">
            <div class="flex justify-end">
              <button
                type="button"
                (click)="nextStep()"
                class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold rounded-2xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-4 focus:ring-blue-500/20 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105"
              >
                <span class="mr-3">Continuer</span>
                <i
                  class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"
                ></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Étape 2 : Adresse -->
      <div *ngIf="currentStep === 2" class="space-y-8 animate-fade-in">
        <!-- Carte principale avec glassmorphism -->
        <div
          class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden"
        >
          <!-- En-tête de section élégant -->
          <div class="bg-gradient-to-r from-emerald-600 to-teal-600 px-8 py-6">
            <div class="flex items-center space-x-4">
              <div
                class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center"
              >
                <i class="fas fa-map-marker-alt text-xl text-white"></i>
              </div>
              <div>
                <h2 class="text-2xl font-bold text-white tracking-tight">
                  Adresse de l'entreprise
                </h2>
                <p class="text-emerald-100 text-sm font-medium">
                  Indiquez l'adresse physique de votre entreprise
                </p>
              </div>
            </div>
          </div>

          <!-- Contenu du formulaire -->
          <div class="p-8">
            <div formGroupName="address" class="space-y-6">
              <!-- Champ : Rue (pleine largeur) -->
              <div class="group">
                <label
                  class="block text-sm font-bold text-gray-800 mb-3 tracking-wide"
                >
                  Adresse complète
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                  >
                    <i
                      class="fas fa-road text-gray-400 group-focus-within:text-emerald-500 transition-colors"
                    ></i>
                  </div>
                  <input
                    type="text"
                    formControlName="street"
                    placeholder="Ex: 123 Avenue de la Libération, Gombe"
                    [ngClass]="{
                      'border-red-400 bg-red-50 focus:border-red-500 focus:ring-red-500/20':
                        submitted && form.get('address.street')?.invalid,
                      'border-emerald-400 bg-emerald-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                        form.get('address.street')?.valid &&
                        form.get('address.street')?.dirty,
                      'border-gray-200 bg-gray-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                        !(submitted && form.get('address.street')?.invalid) &&
                        !(
                          form.get('address.street')?.valid &&
                          form.get('address.street')?.dirty
                        )
                    }"
                    class="block w-full pl-12 pr-12 py-4 rounded-2xl border-2 font-medium text-gray-900 placeholder-gray-400 focus:ring-4 focus:outline-none transition-all duration-300 shadow-sm hover:shadow-md"
                  />
                  <!-- Icône de validation -->
                  <div
                    *ngIf="
                      form.get('address.street')?.valid &&
                      form.get('address.street')?.dirty
                    "
                    class="absolute inset-y-0 right-0 pr-4 flex items-center"
                  >
                    <div
                      class="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center"
                    >
                      <i class="fas fa-check text-xs text-white"></i>
                    </div>
                  </div>
                  <!-- Icône d'erreur -->
                  <div
                    *ngIf="submitted && form.get('address.street')?.invalid"
                    class="absolute inset-y-0 right-0 pr-4 flex items-center"
                  >
                    <div
                      class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
                    >
                      <i class="fas fa-times text-xs text-white"></i>
                    </div>
                  </div>
                </div>
                <!-- Messages d'erreur stylisés -->
                <div
                  *ngIf="submitted && form.get('address.street')?.invalid"
                  class="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl"
                >
                  <div class="flex items-center space-x-2">
                    <i
                      class="fas fa-exclamation-triangle text-red-500 text-sm"
                    ></i>
                    <span class="text-sm font-medium text-red-700">
                      Veuillez entrer une adresse valide
                    </span>
                  </div>
                </div>
              </div>

              <!-- Grille pour ville et code postal -->
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Champ : Ville -->
                <div class="group">
                  <label
                    class="block text-sm font-bold text-gray-800 mb-3 tracking-wide"
                  >
                    Ville
                    <span class="text-red-500 ml-1">*</span>
                  </label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                    >
                      <i
                        class="fas fa-city text-gray-400 group-focus-within:text-emerald-500 transition-colors"
                      ></i>
                    </div>
                    <input
                      type="text"
                      formControlName="city"
                      placeholder="Ex: Kinshasa"
                      [ngClass]="{
                        'border-red-400 bg-red-50 focus:border-red-500 focus:ring-red-500/20':
                          submitted && form.get('address.city')?.invalid,
                        'border-emerald-400 bg-emerald-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                          form.get('address.city')?.valid &&
                          form.get('address.city')?.dirty,
                        'border-gray-200 bg-gray-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                          !(submitted && form.get('address.city')?.invalid) &&
                          !(
                            form.get('address.city')?.valid &&
                            form.get('address.city')?.dirty
                          )
                      }"
                      class="block w-full pl-12 pr-12 py-4 rounded-2xl border-2 font-medium text-gray-900 placeholder-gray-400 focus:ring-4 focus:outline-none transition-all duration-300 shadow-sm hover:shadow-md"
                    />
                    <!-- Icône de validation -->
                    <div
                      *ngIf="
                        form.get('address.city')?.valid &&
                        form.get('address.city')?.dirty
                      "
                      class="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <div
                        class="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center"
                      >
                        <i class="fas fa-check text-xs text-white"></i>
                      </div>
                    </div>
                    <!-- Icône d'erreur -->
                    <div
                      *ngIf="submitted && form.get('address.city')?.invalid"
                      class="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <div
                        class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
                      >
                        <i class="fas fa-times text-xs text-white"></i>
                      </div>
                    </div>
                  </div>
                  <!-- Messages d'erreur stylisés -->
                  <div
                    *ngIf="submitted && form.get('address.city')?.invalid"
                    class="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl"
                  >
                    <div class="flex items-center space-x-2">
                      <i
                        class="fas fa-exclamation-triangle text-red-500 text-sm"
                      ></i>
                      <span class="text-sm font-medium text-red-700">
                        Veuillez entrer une ville
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Champ : Code postal -->
                <div class="group">
                  <label
                    class="block text-sm font-bold text-gray-800 mb-3 tracking-wide"
                  >
                    Code postal
                    <span class="text-red-500 ml-1">*</span>
                  </label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                    >
                      <i
                        class="fas fa-mail-bulk text-gray-400 group-focus-within:text-emerald-500 transition-colors"
                      ></i>
                    </div>
                    <input
                      type="text"
                      formControlName="postalCode"
                      placeholder="Ex: 8200"
                      [ngClass]="{
                        'border-red-400 bg-red-50 focus:border-red-500 focus:ring-red-500/20':
                          submitted && form.get('address.postalCode')?.invalid,
                        'border-emerald-400 bg-emerald-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                          form.get('address.postalCode')?.valid &&
                          form.get('address.postalCode')?.dirty,
                        'border-gray-200 bg-gray-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                          !(
                            submitted && form.get('address.postalCode')?.invalid
                          ) &&
                          !(
                            form.get('address.postalCode')?.valid &&
                            form.get('address.postalCode')?.dirty
                          )
                      }"
                      class="block w-full pl-12 pr-12 py-4 rounded-2xl border-2 font-medium text-gray-900 placeholder-gray-400 focus:ring-4 focus:outline-none transition-all duration-300 shadow-sm hover:shadow-md"
                    />
                    <!-- Icône de validation -->
                    <div
                      *ngIf="
                        form.get('address.postalCode')?.valid &&
                        form.get('address.postalCode')?.dirty
                      "
                      class="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <div
                        class="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center"
                      >
                        <i class="fas fa-check text-xs text-white"></i>
                      </div>
                    </div>
                    <!-- Icône d'erreur -->
                    <div
                      *ngIf="
                        submitted && form.get('address.postalCode')?.invalid
                      "
                      class="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <div
                        class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
                      >
                        <i class="fas fa-times text-xs text-white"></i>
                      </div>
                    </div>
                  </div>
                  <!-- Messages d'erreur stylisés -->
                  <div
                    *ngIf="submitted && form.get('address.postalCode')?.invalid"
                    class="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl"
                  >
                    <div class="flex items-center space-x-2">
                      <i
                        class="fas fa-exclamation-triangle text-red-500 text-sm"
                      ></i>
                      <span class="text-sm font-medium text-red-700">
                        Veuillez entrer un code postal valide
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Champ : Pays -->
              <div class="group">
                <label
                  class="block text-sm font-bold text-gray-800 mb-3 tracking-wide"
                >
                  Pays
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                  >
                    <i
                      class="fas fa-flag text-gray-400 group-focus-within:text-emerald-500 transition-colors"
                    ></i>
                  </div>
                  <select
                    formControlName="country"
                    [ngClass]="{
                      'border-red-400 bg-red-50 focus:border-red-500 focus:ring-red-500/20':
                        submitted && form.get('address.country')?.invalid,
                      'border-emerald-400 bg-emerald-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                        form.get('address.country')?.valid &&
                        form.get('address.country')?.dirty,
                      'border-gray-200 bg-gray-50 focus:border-emerald-500 focus:ring-emerald-500/20':
                        !(submitted && form.get('address.country')?.invalid) &&
                        !(
                          form.get('address.country')?.valid &&
                          form.get('address.country')?.dirty
                        )
                    }"
                    class="block w-full pl-12 pr-12 py-4 rounded-2xl border-2 font-medium text-gray-900 focus:ring-4 focus:outline-none transition-all duration-300 shadow-sm hover:shadow-md appearance-none cursor-pointer"
                  >
                    <option value="" disabled selected>
                      Sélectionnez un pays
                    </option>
                    <option value="République Démocratique du Congo">
                      République Démocratique du Congo
                    </option>
                    <option value="Autre">Autre</option>
                  </select>
                  <!-- Icône de dropdown -->
                  <div
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-4"
                  >
                    <i class="fas fa-chevron-down text-gray-400"></i>
                  </div>
                </div>
                <!-- Messages d'erreur stylisés -->
                <div
                  *ngIf="submitted && form.get('address.country')?.invalid"
                  class="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl"
                >
                  <div class="flex items-center space-x-2">
                    <i
                      class="fas fa-exclamation-triangle text-red-500 text-sm"
                    ></i>
                    <span class="text-sm font-medium text-red-700">
                      Veuillez sélectionner un pays
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Boutons de navigation premium -->
          <div class="bg-gray-50/50 px-8 py-6 border-t border-gray-100">
            <div class="flex justify-between items-center">
              <button
                type="button"
                (click)="prevStep()"
                class="group inline-flex items-center px-6 py-3 bg-white border-2 border-gray-200 text-gray-700 font-semibold rounded-2xl hover:border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-gray-500/20 transition-all duration-300 shadow-sm hover:shadow-md"
              >
                <i
                  class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"
                ></i>
                <span>Précédent</span>
              </button>

              <button
                type="button"
                (click)="nextStep()"
                class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-bold rounded-2xl hover:from-emerald-700 hover:to-teal-700 focus:outline-none focus:ring-4 focus:ring-emerald-500/20 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105"
              >
                <span class="mr-3">Continuer</span>
                <i
                  class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"
                ></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Étape 3 : Paramètres fiscaux -->
      <div *ngIf="currentStep === 3" class="space-y-6 animate-fade-in">
        <h2 class="text-xl font-semibold text-gray-800 border-b pb-2">
          <i class="fas fa-file-invoice-dollar mr-2 text-primary-600"></i>
          Paramètres fiscaux
        </h2>

        <p class="text-gray-600 text-sm">
          Configurez les paramètres fiscaux de base pour votre entreprise. Ces
          valeurs peuvent être ajustées ultérieurement.
        </p>

        <div
          formGroupName="taxSettings"
          class="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          <!-- Colonne gauche -->
          <div class="space-y-6">
            <!-- Champ : Taux d'imposition -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Taux d'imposition sur le revenu (%)
                <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="incomeTaxRate"
                  placeholder="0"
                  min="0"
                  max="100"
                  step="0.1"
                  [ngClass]="{
                    'border-red-500':
                      submitted &&
                      form.get('taxSettings.incomeTaxRate')?.invalid,
                    'border-green-500':
                      form.get('taxSettings.incomeTaxRate')?.valid &&
                      form.get('taxSettings.incomeTaxRate')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('taxSettings.incomeTaxRate')?.valid &&
                    form.get('taxSettings.incomeTaxRate')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted && form.get('taxSettings.incomeTaxRate')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                Doit être compris entre 0 et 100
              </div>
            </div>

            <!-- Champ : Taux de sécurité sociale -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Taux de sécurité sociale (%) <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="socialSecurityRate"
                  placeholder="0"
                  min="0"
                  max="100"
                  step="0.1"
                  [ngClass]="{
                    'border-red-500':
                      submitted &&
                      form.get('taxSettings.socialSecurityRate')?.invalid,
                    'border-green-500':
                      form.get('taxSettings.socialSecurityRate')?.valid &&
                      form.get('taxSettings.socialSecurityRate')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('taxSettings.socialSecurityRate')?.valid &&
                    form.get('taxSettings.socialSecurityRate')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted &&
                  form.get('taxSettings.socialSecurityRate')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                Doit être compris entre 0 et 100
              </div>
            </div>

            <!-- Champ : Taux d'assurance chômage -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Taux d'assurance chômage (%) <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="unEmploymentInsuranceRate"
                  placeholder="0"
                  min="0"
                  max="100"
                  step="0.1"
                  [ngClass]="{
                    'border-red-500':
                      submitted &&
                      form.get('taxSettings.unEmploymentInsuranceRate')
                        ?.invalid,
                    'border-green-500':
                      form.get('taxSettings.unEmploymentInsuranceRate')
                        ?.valid &&
                      form.get('taxSettings.unEmploymentInsuranceRate')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('taxSettings.unEmploymentInsuranceRate')?.valid &&
                    form.get('taxSettings.unEmploymentInsuranceRate')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted &&
                  form.get('taxSettings.unEmploymentInsuranceRate')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                Doit être compris entre 0 et 100
              </div>
            </div>
          </div>

          <!-- Colonne droite -->
          <div class="space-y-6">
            <!-- Champ : Taux d'assurance maladie -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Taux d'assurance maladie (%) <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="healthInsuranceRate"
                  placeholder="0"
                  min="0"
                  max="100"
                  step="0.1"
                  [ngClass]="{
                    'border-red-500':
                      submitted &&
                      form.get('taxSettings.healthInsuranceRate')?.invalid,
                    'border-green-500':
                      form.get('taxSettings.healthInsuranceRate')?.valid &&
                      form.get('taxSettings.healthInsuranceRate')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('taxSettings.healthInsuranceRate')?.valid &&
                    form.get('taxSettings.healthInsuranceRate')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted &&
                  form.get('taxSettings.healthInsuranceRate')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                Doit être compris entre 0 et 100
              </div>
            </div>

            <!-- Champ : Taux de cotisation retraite -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Taux de cotisation retraite (%)
                <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="pensionContributionRate"
                  placeholder="0"
                  min="0"
                  max="100"
                  step="0.1"
                  [ngClass]="{
                    'border-red-500':
                      submitted &&
                      form.get('taxSettings.pensionContributionRate')?.invalid,
                    'border-green-500':
                      form.get('taxSettings.pensionContributionRate')?.valid &&
                      form.get('taxSettings.pensionContributionRate')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('taxSettings.pensionContributionRate')?.valid &&
                    form.get('taxSettings.pensionContributionRate')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted &&
                  form.get('taxSettings.pensionContributionRate')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                Doit être compris entre 0 et 100
              </div>
            </div>

            <!-- Champ : Seuil d'imposition -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Seuil d'imposition (optionnel)
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="incomeTaxThreshold"
                  placeholder="Ex: 10000"
                  min="0"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
              </div>
            </div>

            <!-- Champ : Fréquence de paiement des taxes -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Fréquence de paiement des taxes
              </label>
              <div class="relative">
                <select
                  formControlName="taxPaymentFrequency"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all appearance-none bg-white"
                >
                  <option value="MONTHLY">Mensuel</option>
                  <option value="QUARTERLY">Trimestriel</option>
                  <option value="ANNUAL">Annuel</option>
                </select>
                <div
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700"
                >
                  <i class="fas fa-chevron-down"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-between pt-4">
          <button
            type="button"
            (click)="prevStep()"
            class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            <i class="fas fa-arrow-left mr-2"></i> Précédent
          </button>
          <button
            type="button"
            (click)="nextStep()"
            class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
          >
            Suivant <i class="fas fa-arrow-right ml-2"></i>
          </button>
        </div>
      </div>

      <!-- Étape 4 : Configuration paie -->
      <div *ngIf="currentStep === 4" class="space-y-6 animate-fade-in">
        <h2 class="text-xl font-semibold text-gray-800 border-b pb-2">
          <i class="fas fa-money-bill-wave mr-2 text-primary-600"></i>
          Configuration de la paie
        </h2>

        <div
          formGroupName="payrollConfiguration"
          class="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          <!-- Colonne gauche -->
          <div class="space-y-6">
            <!-- Champ : Cycle de paie -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Cycle de paie <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <select
                  formControlName="payrollCycle"
                  [ngClass]="{
                    'border-red-500':
                      submitted &&
                      form.get('payrollConfiguration.payrollCycle')?.invalid,
                    'border-green-500':
                      form.get('payrollConfiguration.payrollCycle')?.valid &&
                      form.get('payrollConfiguration.payrollCycle')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all appearance-none bg-white"
                >
                  <option value="MONTHLY">Mensuel</option>
                  <option value="SEMI_MONTHLY">Semi-mensuel</option>
                  <option value="BI_WEEKLY">Bi-hebdomadaire</option>
                  <option value="WEEKLY">Hebdomadaire</option>
                </select>
                <div
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700"
                >
                  <i class="fas fa-chevron-down"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted &&
                  form.get('payrollConfiguration.payrollCycle')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                Veuillez sélectionner un cycle de paie
              </div>
            </div>

            <!-- Champ : Jour de paiement -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Jour de paiement <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="paymentDay"
                  placeholder="Ex: 5 (pour le 5 du mois)"
                  min="1"
                  max="31"
                  [ngClass]="{
                    'border-red-500':
                      submitted &&
                      form.get('payrollConfiguration.paymentDay')?.invalid,
                    'border-green-500':
                      form.get('payrollConfiguration.paymentDay')?.valid &&
                      form.get('payrollConfiguration.paymentDay')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('payrollConfiguration.paymentDay')?.valid &&
                    form.get('payrollConfiguration.paymentDay')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted &&
                  form.get('payrollConfiguration.paymentDay')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                Doit être compris entre 1 et 31
              </div>
            </div>

            <!-- Champ : Multiplicateur d'heures supplémentaires -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Multiplicateur d'heures supplémentaires
                <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="overtimeMultiplier"
                  placeholder="Ex: 1.5"
                  min="1"
                  step="0.1"
                  [ngClass]="{
                    'border-red-500':
                      submitted &&
                      form.get('payrollConfiguration.overtimeMultiplier')
                        ?.invalid,
                    'border-green-500':
                      form.get('payrollConfiguration.overtimeMultiplier')
                        ?.valid &&
                      form.get('payrollConfiguration.overtimeMultiplier')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('payrollConfiguration.overtimeMultiplier')
                      ?.valid &&
                    form.get('payrollConfiguration.overtimeMultiplier')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted &&
                  form.get('payrollConfiguration.overtimeMultiplier')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                Doit être supérieur ou égal à 1
              </div>
            </div>
          </div>

          <!-- Colonne droite -->
          <div class="space-y-6">
            <!-- Champ : Heures supplémentaires max -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Heures supplémentaires max par période (optionnel)
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="maxOvertimeHours"
                  placeholder="Ex: 10"
                  min="0"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
              </div>
            </div>

            <!-- Champ : Type de bonus -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Type de bonus
              </label>
              <div class="relative">
                <select
                  formControlName="bonusType"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all appearance-none bg-white"
                >
                  <option value="PERCENTAGE_OF_SALARY">
                    Pourcentage du salaire
                  </option>
                  <option value="FIXED_AMOUNT">Montant fixe</option>
                  <option value="NONE">Aucun</option>
                </select>
                <div
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700"
                >
                  <i class="fas fa-chevron-down"></i>
                </div>
              </div>
            </div>

            <!-- Champ : Taux de bonus de performance -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Taux de bonus de performance (optionnel)
              </label>
              <div class="relative">
                <input
                  type="number"
                  formControlName="performanceBonusRate"
                  placeholder="Ex: 10"
                  min="0"
                  max="100"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-between pt-4">
          <button
            type="button"
            (click)="prevStep()"
            class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            <i class="fas fa-arrow-left mr-2"></i> Précédent
          </button>
          <button
            type="button"
            (click)="nextStep()"
            class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
          >
            Suivant <i class="fas fa-arrow-right ml-2"></i>
          </button>
        </div>
      </div>

      <!-- Étape 5 : Informations supplémentaires -->
      <div *ngIf="currentStep === 5" class="space-y-6 animate-fade-in">
        <h2 class="text-xl font-semibold text-gray-800 border-b pb-2">
          <i class="fas fa-info-circle mr-2 text-primary-600"></i>
          Informations supplémentaires
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Colonne gauche -->
          <div class="space-y-6">
            <!-- Champ : Nom officiel -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Nom officiel (raison sociale)
                <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="text"
                  formControlName="officialName"
                  placeholder="Ex: ENTREPRISE LUMINA SARL"
                  [ngClass]="{
                    'border-red-500':
                      submitted && form.get('officialName')?.invalid,
                    'border-green-500':
                      form.get('officialName')?.valid &&
                      form.get('officialName')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('officialName')?.valid &&
                    form.get('officialName')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="submitted && form.get('officialName')?.invalid"
                class="mt-1 text-sm text-red-600"
              >
                Veuillez entrer le nom officiel de l'entreprise
              </div>
            </div>

            <!-- Champ : Numéro d'identification fiscale -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Numéro d'identification fiscale
                <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="text"
                  formControlName="taxIdentificationNumber"
                  placeholder="Ex: CD123456789"
                  [ngClass]="{
                    'border-red-500':
                      submitted && form.get('taxIdentificationNumber')?.invalid,
                    'border-green-500':
                      form.get('taxIdentificationNumber')?.valid &&
                      form.get('taxIdentificationNumber')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('taxIdentificationNumber')?.valid &&
                    form.get('taxIdentificationNumber')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="
                  submitted && form.get('taxIdentificationNumber')?.invalid
                "
                class="mt-1 text-sm text-red-600"
              >
                {{ form.get('taxIdentificationNumber')?.errors?.['required'] ? 'Ce champ est obligatoire' : 'Caractères alphanumériques seulement' }}
              </div>
            </div>
          </div>

          <!-- Colonne droite -->
          <div class="space-y-6">
            <!-- Champ : Secteur d'activité -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Secteur d'activité <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="text"
                  formControlName="industry"
                  placeholder="Ex: Informatique, Restauration, Conseil..."
                  [ngClass]="{
                    'border-red-500':
                      submitted && form.get('industry')?.invalid,
                    'border-green-500':
                      form.get('industry')?.valid && form.get('industry')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                />
                <div
                  *ngIf="
                    form.get('industry')?.valid && form.get('industry')?.dirty
                  "
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div
                *ngIf="submitted && form.get('industry')?.invalid"
                class="mt-1 text-sm text-red-600"
              >
                Veuillez indiquer le secteur d'activité
              </div>
            </div>

            <!-- Champ : Description -->
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Description de l'entreprise (optionnel)
              </label>
              <div class="relative">
                <textarea
                  formControlName="description"
                  rows="4"
                  placeholder="Décrivez en quelques mots les activités de votre entreprise..."
                  [ngClass]="{
                    'border-red-500':
                      submitted && form.get('description')?.invalid,
                    'border-green-500':
                      form.get('description')?.valid &&
                      form.get('description')?.dirty
                  }"
                  class="block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all"
                ></textarea>
                <div
                  *ngIf="
                    form.get('description')?.valid &&
                    form.get('description')?.dirty
                  "
                  class="absolute top-3 right-3 text-green-500"
                >
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              <div class="flex justify-between mt-1">
                <div
                  *ngIf="submitted && form.get('description')?.invalid"
                  class="text-sm text-red-600"
                >
                  Maximum 500 caractères
                </div>
                <div class="text-xs text-gray-500">
                  {{ form.get("description")?.value?.length || 0 }}/500
                  caractères
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-between pt-4">
          <button
            type="button"
            (click)="prevStep()"
            class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            <i class="fas fa-arrow-left mr-2"></i> Précédent
          </button>
          <button
            type="button"
            (click)="nextStep()"
            class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
          >
            Suivant <i class="fas fa-arrow-right ml-2"></i>
          </button>
        </div>
      </div>

      <!-- Étape 6 : Confirmation -->
      <div *ngIf="currentStep === 6" class="space-y-6 animate-fade-in">
        <h2 class="text-xl font-semibold text-gray-800 border-b pb-2">
          <i class="fas fa-check-circle mr-2 text-green-600"></i>
          Confirmation
        </h2>

        <div class="bg-gray-50 rounded-lg border border-gray-200">
          <div class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              Récapitulatif des informations
            </h3>
          </div>

          <div class="confirmation-scroll overflow-y-auto p-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Section Informations de base -->
              <div class="space-y-3">
                <h4 class="font-medium text-gray-800 border-b pb-1">
                  <i class="fas fa-building mr-2 text-blue-600"></i>
                  Informations de base
                </h4>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Nom:</span>
                  <span class="text-gray-600 break-words">{{
                    form.value.companyName
                  }}</span>
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Email:</span>
                  <span class="text-gray-600 break-all">{{
                    form.value.email
                  }}</span>
                </p>
                <p class="text-sm" *ngIf="form.value.phoneNumbers?.length">
                  <span class="font-semibold text-gray-700">Téléphones:</span>
                  <span class="text-gray-600 break-words">{{
                    form.value.phoneNumbers?.join(", ")
                  }}</span>
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Site web:</span>
                  <span class="text-gray-600 break-all">{{
                    form.value.website
                  }}</span>
                </p>
                <p class="text-sm" *ngIf="form.value.logo">
                  <span class="font-semibold text-gray-700">Logo:</span>
                  <span
                    class="text-gray-600 break-all max-w-full overflow-hidden"
                  >
                    <span
                      class="inline-block max-w-full truncate"
                      [title]="form.value.logo"
                      >{{ form.value.logo }}</span
                    >
                  </span>
                </p>
              </div>

              <!-- Section Adresse -->
              <div class="space-y-3">
                <h4 class="font-medium text-gray-800 border-b pb-1">
                  <i class="fas fa-map-marker-alt mr-2 text-blue-600"></i>
                  Adresse
                </h4>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Adresse:</span>
                  <span class="text-gray-600 break-words">{{
                    form.value.address?.street
                  }}</span>
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Ville:</span>
                  <span class="text-gray-600 break-words"
                    >{{ form.value.address?.postalCode }}
                    {{ form.value.address?.city }}</span
                  >
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Pays:</span>
                  <span class="text-gray-600 break-words">{{
                    form.value.address?.country
                  }}</span>
                </p>
              </div>

              <!-- Section Paramètres fiscaux -->
              <div class="space-y-3">
                <h4 class="font-medium text-gray-800 border-b pb-1">
                  <i class="fas fa-file-invoice-dollar mr-2 text-blue-600"></i>
                  Paramètres fiscaux
                </h4>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700"
                    >Impôt sur le revenu:</span
                  >
                  <span class="text-gray-600"
                    >{{ form.value.taxSettings?.incomeTaxRate }}%</span
                  >
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700"
                    >Sécurité sociale:</span
                  >
                  <span class="text-gray-600"
                    >{{ form.value.taxSettings?.socialSecurityRate }}%</span
                  >
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700"
                    >Assurance chômage:</span
                  >
                  <span class="text-gray-600"
                    >{{
                      form.value.taxSettings?.unEmploymentInsuranceRate
                    }}%</span
                  >
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700"
                    >Assurance maladie:</span
                  >
                  <span class="text-gray-600"
                    >{{ form.value.taxSettings?.healthInsuranceRate }}%</span
                  >
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Retraite:</span>
                  <span class="text-gray-600"
                    >{{
                      form.value.taxSettings?.pensionContributionRate
                    }}%</span
                  >
                </p>
              </div>

              <!-- Section Configuration paie -->
              <div class="space-y-3">
                <h4 class="font-medium text-gray-800 border-b pb-1">
                  <i class="fas fa-money-bill-wave mr-2 text-blue-600"></i>
                  Configuration paie
                </h4>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Cycle:</span>
                  <span class="text-gray-600">{{
                    getPayrollCycleLabel(
                      form.value.payrollConfiguration?.payrollCycle
                    )
                  }}</span>
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700"
                    >Jour de paiement:</span
                  >
                  <span class="text-gray-600">{{
                    form.value.payrollConfiguration?.paymentDay
                  }}</span>
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700"
                    >Multiplicateur heures sup:</span
                  >
                  <span class="text-gray-600"
                    >{{
                      form.value.payrollConfiguration?.overtimeMultiplier
                    }}x</span
                  >
                </p>
                <p
                  class="text-sm"
                  *ngIf="form.value.payrollConfiguration?.maxOvertimeHours"
                >
                  <span class="font-semibold text-gray-700"
                    >Heures sup max:</span
                  >
                  <span class="text-gray-600"
                    >{{
                      form.value.payrollConfiguration?.maxOvertimeHours
                    }}h</span
                  >
                </p>
              </div>

              <!-- Section Informations supplémentaires -->
              <div class="md:col-span-2 space-y-3">
                <h4 class="font-medium text-gray-800 border-b pb-1">
                  <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                  Informations supplémentaires
                </h4>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Nom officiel:</span>
                  <span class="text-gray-600 break-words">{{
                    form.value.officialName
                  }}</span>
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700"
                    >Numéro fiscal:</span
                  >
                  <span class="text-gray-600 break-all">{{
                    form.value.taxIdentificationNumber
                  }}</span>
                </p>
                <p class="text-sm">
                  <span class="font-semibold text-gray-700">Secteur:</span>
                  <span class="text-gray-600 break-words">{{
                    form.value.industry
                  }}</span>
                </p>
                <p class="text-sm" *ngIf="form.value.description">
                  <span class="font-semibold text-gray-700">Description:</span>
                  <span
                    class="text-gray-600 break-words max-w-full overflow-hidden"
                  >
                    <span
                      class="inline-block max-w-full"
                      [title]="form.value.description"
                      >{{ form.value.description }}</span
                    >
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-3 pt-4">
          <input
            type="checkbox"
            id="termsAgreement"
            [(ngModel)]="termsAgreed"
            [ngModelOptions]="{ standalone: true }"
            class="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
          <label for="termsAgreement" class="text-sm text-gray-700">
            Je certifie que les informations fournies sont exactes et j'accepte
            les
            <a href="#" class="text-primary-600 hover:underline"
              >conditions générales</a
            >.
          </label>
        </div>

        <div class="flex justify-between pt-4">
          <button
            type="button"
            (click)="prevStep()"
            class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            <i class="fas fa-arrow-left mr-2"></i> Précédent
          </button>
          <button
            type="button"
            (click)="onSubmit()"
            [disabled]="!termsAgreed || isLoading"
            [ngClass]="{
              'bg-primary-600 hover:bg-primary-700': termsAgreed && !isLoading,
              'bg-gray-400 cursor-not-allowed': !termsAgreed || isLoading
            }"
            class="px-6 py-3 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
          >
            <span *ngIf="!isLoading">
              <i class="fas fa-check-circle mr-2"></i> Créer l'entreprise
            </span>
            <span *ngIf="isLoading">
              <i class="fas fa-spinner fa-spin mr-2"></i> Création en cours...
            </span>
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
