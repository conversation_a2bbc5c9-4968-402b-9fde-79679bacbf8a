<!-- Style Twenty: Formulaire d'inscription centré -->
<form class="space-y-6" [formGroup]="form" (ngSubmit)="onSubmit()">
  <!-- En-tête -->
  <div class="text-center">
    <h2 class="text-2xl font-bold text-black mb-2">
      Créez votre espace de travail
    </h2>
    <p class="text-black/70 text-sm">
      Commencez votre essai gratuit de 30 jours
    </p>
  </div>

  <!-- Bouton Google -->
  <div routerLink="/dashboard">
    <button
      type="button"
      class="w-full flex items-center justify-center px-4 py-3 bg-white backdrop-blur-sm border border-white/30 rounded-xl text-black font-medium hover:bg-black/30 transition-all duration-300 group"
    >
      <svg-icon src="assets/icons/google-logo.svg" [svgClass]="'h-5 w-5 mr-3'">
      </svg-icon>
      Continuer avec Google
    </button>
  </div>

  <!-- Bouton Microsoft -->
  <div routerLink="/dashboard">
    <button
      type="button"
      class="w-full flex items-center justify-center px-4 py-3 bg-white backdrop-blur-sm border border-white/30 rounded-xl text-black font-medium hover:bg-white/30 transition-all duration-300 group"
    >
      <svg class="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="currentColor">
        <path
          d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z"
        />
      </svg>
      Continuer avec Microsoft
    </button>
  </div>

  <!-- Séparateur -->
  <div class="relative">
    <div class="absolute inset-0 flex items-center">
      <div class="w-full border-t border-black/20"></div>
    </div>
    <div class="relative flex justify-center text-sm">
      <span class="px-2 bg-transparent text-black/60">ou</span>
    </div>
  </div>

  <!-- Champs de formulaire -->
  <div class="space-y-4">
    <!-- Prénom et Nom -->
    <div class="grid grid-cols-2 gap-4">
      <div>
        <label
          for="firstName"
          class="block text-sm font-medium text-black/80 mb-2"
        >
          Prénom
        </label>
        <input
          type="text"
          id="firstName"
          formControlName="firstName"
          placeholder="Jean"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
        />
        <div
          *ngIf="submitted && f['firstName'].errors"
          class="text-red-300 text-sm mt-1"
        >
          <div *ngIf="f['firstName'].errors['required']">
            Le prénom est requis
          </div>
        </div>
      </div>

      <div>
        <label
          for="lastName"
          class="block text-sm font-medium text-black/80 mb-2"
        >
          Nom
        </label>
        <input
          type="text"
          id="lastName"
          formControlName="lastName"
          placeholder="Dupont"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
        />
        <div
          *ngIf="submitted && f['lastName'].errors"
          class="text-red-300 text-sm mt-1"
        >
          <div *ngIf="f['lastName'].errors['required']">Le nom est requis</div>
        </div>
      </div>
    </div>

    <!-- Email -->
    <div>
      <label for="email" class="block text-sm font-medium text-black/80 mb-2">
        Adresse email
      </label>
      <input
        type="email"
        id="email"
        formControlName="email"
        placeholder="<EMAIL>"
        class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300"
      />
      <div
        *ngIf="submitted && f['email'].errors"
        class="text-red-300 text-sm mt-1"
      >
        <div *ngIf="f['email'].errors['required']">L'email est requis</div>
        <div *ngIf="f['email'].errors['email']">L'email doit être valide</div>
      </div>
    </div>

    <!-- Mot de passe -->
    <div>
      <label
        for="password"
        class="block text-sm font-medium text-black/80 mb-2"
      >
        Mot de passe
      </label>
      <div class="relative">
        <input
          [type]="passwordTextType ? 'text' : 'password'"
          id="password"
          formControlName="password"
          placeholder="Votre mot de passe"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300 pr-12"
        />
        <button
          type="button"
          class="absolute inset-y-0 right-0 pr-3 flex items-center text-black/60 hover:text-black/80"
          (click)="togglePasswordTextType()"
        >
          <svg-icon
            [src]="
              !passwordTextType
                ? 'assets/icons/heroicons/outline/eye-off.svg'
                : 'assets/icons/heroicons/outline/eye.svg'
            "
            [svgClass]="'h-5 w-5'"
          >
          </svg-icon>
        </button>
      </div>
      <div
        *ngIf="submitted && f['password'].errors"
        class="text-red-300 text-sm mt-1"
      >
        <div *ngIf="f['password'].errors['required']">
          Le mot de passe est requis
        </div>
        <div *ngIf="f['password'].errors['minLength']">
          Le mot de passe doit comporter au moins 8 caractères
        </div>
      </div>
    </div>

    <!-- Confirmer le mot de passe -->
    <div>
      <label
        for="confirmPassword"
        class="block text-sm font-medium text-black/80 mb-2"
      >
        Confirmer le mot de passe
      </label>
      <div class="relative">
        <input
          [type]="confirmPasswordTextType ? 'text' : 'password'"
          id="confirmPassword"
          formControlName="confirmPassword"
          placeholder="Confirmez votre mot de passe"
          class="w-full px-4 py-3 bg-black/10 backdrop-blur-sm border border-black/20 rounded-xl text-black placeholder-black/50 focus:outline-none focus:ring-2 focus:ring-black/30 focus:border-black/40 transition-all duration-300 pr-12"
        />
        <button
          type="button"
          class="absolute inset-y-0 right-0 pr-3 flex items-center text-black/60 hover:text-black/80"
          (click)="toggleConfirmPasswordTextType()"
        >
          <svg-icon
            [src]="
              !confirmPasswordTextType
                ? 'assets/icons/heroicons/outline/eye-off.svg'
                : 'assets/icons/heroicons/outline/eye.svg'
            "
            [svgClass]="'h-5 w-5'"
          >
          </svg-icon>
        </button>
      </div>
      <div
        *ngIf="submitted && f['confirmPassword'].errors"
        class="text-red-300 text-sm mt-1"
      >
        <div *ngIf="f['confirmPassword'].errors['required']">
          La confirmation du mot de passe est requise
        </div>
        <div *ngIf="form.errors?.['mismatch']">
          Les mots de passe ne correspondent pas
        </div>
      </div>
    </div>
  </div>

  <!-- Conditions générales -->
  <div class="flex items-center">
    <input
      id="accept-term"
      name="accept-term"
      type="checkbox"
      class="h-4 w-4 text-black/20 focus:ring-black/30 border-black/20 rounded bg-black/10"
    />
    <label for="accept-term" class="ml-2 block text-sm text-black/70">
      En utilisant Twenty, vous acceptez les
      <a href="#" class="text-black underline hover:text-black/80"
        >Conditions d'utilisation</a
      >
      et la
      <a href="#" class="text-black underline hover:text-black/80"
        >Politique de confidentialité</a
      >
    </label>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="errorMessage" class="text-red-300 text-sm text-center">
    {{ errorMessage }}
  </div>

  <!-- Bouton d'inscription -->
  <app-button
    full
    class="mt-4"
    impact="bold"
    tone="primary"
    shape="rounded"
    size="medium"
  >
    <span *ngIf="!isLoading">Continuer</span>
    <span *ngIf="isLoading" class="flex items-center">
      <svg
        class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-900"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      Inscription...
    </span>
  </app-button>

  <!-- Lien de connexion -->
  <div class="text-center">
    <span class="text-black/60 text-sm">Vous avez déjà un compte ? </span>
    <a
      routerLink="/auth/sign-in"
      class="text-black font-medium hover:text-black/80 transition-colors"
    >
      Se connecter
    </a>
  </div>
</form>
