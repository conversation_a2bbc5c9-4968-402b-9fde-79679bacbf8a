<form class="my-10 space-y-6" [formGroup]="form" (ngSubmit)="onSubmit()">
  <div class="text-left">
    <h2 class="mb-1 text-3xl font-semibold text-foreground">
      S'inscrire <span class="text-primary">!</span>
    </h2>
    <p class="text-sm text-muted-foreground">
      Commençons avec votre essai gratuit de 30 jours
    </p>
  </div>

  <div routerLink="/dashboard">
    <app-button full impact="bold" tone="light" shape="rounded" size="medium">
      <svg-icon src="assets/icons/google-logo.svg" [svgClass]="'h-6 w-6 mr-2'">
      </svg-icon>
      S'inscrire avec Google
    </app-button>
  </div>

  <div
    class="my-4 flex items-center before:mt-0.5 before:flex-1 before:border-t before:border-muted after:mt-0.5 after:flex-1 after:border-t after:border-muted"
  >
    <p class="mx-4 mb-0 text-center text-sm text-muted-foreground">ou</p>
  </div>

  <div class="space-y-3 text-left">
    <!-- Prénom -->
    <div class="form__group">
      <div class="relative">
        <input
          type="text"
          id="firstName"
          [ngClass]="{
            'is__invalid-input': submitted && f['firstName'].errors
          }"
          class="peer block"
          placeholder=" "
          formControlName="firstName"
        />
        <label
          for="firstName"
          class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >
          Prénom
        </label>
      </div>
      <div *ngIf="submitted && f['firstName'].errors" class="is__invalid-error">
        <div *ngIf="f['firstName'].errors['required']">
          Le prénom est requis
        </div>
      </div>
    </div>

    <!-- Nom -->
    <div class="form__group">
      <div class="relative">
        <input
          type="text"
          id="lastName"
          [ngClass]="{ 'is__invalid-input': submitted && f['lastName'].errors }"
          class="peer block"
          placeholder=" "
          formControlName="lastName"
        />
        <label
          for="lastName"
          class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >
          Nom
        </label>
      </div>
      <div *ngIf="submitted && f['lastName'].errors" class="is__invalid-error">
        <div *ngIf="f['lastName'].errors['required']">Le nom est requis</div>
      </div>
    </div>

    <!-- Email -->
    <div class="form__group">
      <div class="relative">
        <input
          type="text"
          id="email"
          [ngClass]="{ 'is__invalid-input': submitted && f['email'].errors }"
          class="peer block"
          placeholder=" "
          formControlName="email"
        />
        <label
          for="email"
          class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >
          Adresse email
        </label>
      </div>
      <div *ngIf="submitted && f['email'].errors" class="is__invalid-error">
        <div *ngIf="f['email'].errors['required']">L'email est requis</div>
        <div *ngIf="f['email'].errors['email']">L'email doit être valide</div>
      </div>
    </div>

    <!-- Mot de passe -->
    <div class="form__group">
      <div class="relative">
        <input
          [type]="passwordTextType ? 'text' : 'password'"
          id="password"
          [ngClass]="{ 'is__invalid-input': submitted && f['password'].errors }"
          class="peer block"
          placeholder=" "
          formControlName="password"
        />
        <label
          for="password"
          class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >
          Mot de passe
        </label>
        <span
          class="absolute top-2.5 right-5 cursor-pointer text-muted-foreground"
          (click)="togglePasswordTextType()"
        >
          <svg-icon
            [src]="
              !passwordTextType
                ? 'assets/icons/heroicons/outline/eye-off.svg'
                : 'assets/icons/heroicons/outline/eye.svg'
            "
            [svgClass]="'h-5 w-5'"
          >
          </svg-icon>
        </span>
      </div>
      <div *ngIf="submitted && f['password'].errors" class="is__invalid-error">
        <div *ngIf="f['password'].errors['required']">
          Le mot de passe est requis
        </div>
        <div *ngIf="f['password'].errors['minLength']">
          Le mot de passe doit comporter au moins 8 caractères
        </div>
      </div>
    </div>

    <!-- Confirmer le mot de passe -->
    <div class="form__group">
      <div class="relative">
        <input
          [type]="confirmPasswordTextType ? 'text' : 'password'"
          id="confirmPassword"
          [ngClass]="{
            'is__invalid-input': submitted && f['confirmPassword'].errors
          }"
          class="peer block"
          placeholder=" "
          formControlName="confirmPassword"
        />
        <label
          for="confirmPassword"
          class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >
          Confirmer le mot de passe
        </label>
        <span
          class="absolute top-2.5 right-5 cursor-pointer text-muted-foreground"
          (click)="toggleConfirmPasswordTextType()"
        >
          <svg-icon
            [src]="
              !confirmPasswordTextType
                ? 'assets/icons/heroicons/outline/eye-off.svg'
                : 'assets/icons/heroicons/outline/eye.svg'
            "
            [svgClass]="'h-5 w-5'"
          >
          </svg-icon>
        </span>
      </div>
      <div
        *ngIf="submitted && f['confirmPassword'].errors"
        class="is__invalid-error"
      >
        <div *ngIf="f['confirmPassword'].errors['required']">
          La confirmation du mot de passe est requise
        </div>
        <div *ngIf="form.errors?.['mismatch']">
          Les mots de passe ne correspondent pas
        </div>
      </div>
    </div>
  </div>

  <!-- Conditions générales -->
  <div class="flex items-center justify-between space-x-3">
    <div class="flex items-center gap-1">
      <input id="accept-term" name="accept-term" type="checkbox" />
      <label for="accept-term" class="ml-2 block text-sm text-muted-foreground">
        J'accepte les
      </label>
      <app-button impact="none" tone="primary" shape="rounded" size="small">
        Conditions
      </app-button>
    </div>
  </div>

  <!-- Bouton d'envoi -->
  <div>
    <app-button full impact="bold" tone="primary" shape="rounded" size="medium">
      <span *ngIf="!isLoading">S'inscrire</span>
      <span *ngIf="isLoading">Chargement...</span>
    </app-button>
  </div>

  <!-- Lien de connexion -->
  <div class="flex items-center text-sm text-muted-foreground">
    Vous avez déjà un compte ?
    <app-button
      routerLink="/auth/sign-in"
      impact="none"
      tone="primary"
      shape="rounded"
      size="small"
    >
      Se connecter
    </app-button>
  </div>
</form>
