import { Component, OnInit } from '@angular/core';
import { select, Store } from '@ngrx/store';
import { ApplicationService } from 'src/app/core/services/job-offer/application.service';
import {
  Application,
  ApplicationStatusEnum,
} from 'src/app/core/services/job-offer/job-offer.model';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-applications',
  templateUrl: './applications.component.html',
  standalone: false,
})
export class ApplicationsComponent implements OnInit {
  applications: Application[] = [];
  loading = false;
  error: string | null = null;
  ApplicationStatusEnum = ApplicationStatusEnum;
  currentCompanyId!: string;

  constructor(
    private applicationService: ApplicationService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.currentCompanyId = company.id!;
    });
    this.loadApplications();
  }

  loadApplications(): void {
    this.loading = true;
    this.applicationService.findAll(this.currentCompanyId).subscribe({
      next: (applications) => {
        this.applications = applications;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Erreur lors du chargement des candidatures';
        this.loading = false;
      },
    });
  }

  getStatusColor(status: ApplicationStatusEnum): string {
    switch (status) {
      case ApplicationStatusEnum.APPROVED:
        return 'bg-green-100 text-green-800';
      case ApplicationStatusEnum.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  }

  getStatusLabel(status: ApplicationStatusEnum): string {
    switch (status) {
      case ApplicationStatusEnum.APPROVED:
        return 'Approuvée';
      case ApplicationStatusEnum.REJECTED:
        return 'Rejetée';
      default:
        return 'En attente';
    }
  }
}
