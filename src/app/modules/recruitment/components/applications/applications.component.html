<div class="flex flex-col rounded-xl bg-white p-6 dark:bg-gray-800">
  <!-- Header -->
  <div class="mb-6 flex items-center justify-between">
    <div class="flex items-center gap-4">
      <div class="flex flex-col">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">
          Candidatures
        </h3>
        <span class="text-xs text-gray-500 dark:text-gray-400"
          >{{ applications.length }} candidature(s)</span
        >
      </div>
      <button
        (click)="loadApplications()"
        class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
        title="Recharger les données"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
      </button>
    </div>
  </div>

  <!-- Loading state -->
  <div *ngIf="loading" class="flex flex-col items-center justify-center py-12">
    <div
      class="h-10 w-10 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"
    ></div>
    <p class="mt-3 text-sm text-gray-600 dark:text-gray-400">
      Chargement des candidatures...
    </p>
  </div>

  <!-- Error state -->
  <div
    *ngIf="error"
    class="mb-6 rounded-lg bg-red-50 p-4 text-red-700 dark:bg-red-900/30 dark:text-red-400"
  >
    <p>{{ error }}</p>
  </div>

  <!-- Empty state -->
  <div
    *ngIf="!loading && !error && applications.length === 0"
    class="flex flex-col items-center justify-center py-12"
  >
    <svg
      class="h-12 w-12 text-gray-400"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="1.5"
        d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
    <h3 class="mt-3 text-lg font-medium text-gray-700 dark:text-gray-300">
      Aucune candidature
    </h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Aucune candidature n'a été soumise pour le moment.
    </p>
  </div>

  <!-- Applications grid -->
  <div
    *ngIf="!loading && !error && applications.length > 0"
    class="grid grid-cols-1 gap-6"
  >
    <div
      *ngFor="let application of applications"
      class="rounded-xl border border-gray-200 bg-white shadow-sm transition-all hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
    >
      <a
        [routerLink]="['/dashboard/recruitment/applications', application.id]"
        class="block p-5"
      >
        <!-- Application header -->
        <div class="flex items-start justify-between">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div
                class="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center dark:bg-gray-700"
              >
                <span
                  class="text-lg font-medium text-gray-600 dark:text-gray-300"
                >
                  {{ application.user?.firstName?.charAt(0)
                  }}{{ application.user?.lastName?.charAt(0) }}
                </span>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                {{ application.user?.firstName }}
                {{ application.user?.lastName }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ application.jobOffer?.title }}
              </p>
            </div>
          </div>
          <span
            [class]="
              'px-3 py-1 rounded-full text-xs font-medium ' +
              getStatusColor(application.status)
            "
          >
            {{ getStatusLabel(application.status) }}
          </span>
        </div>

        <!-- Application details -->
        <div class="mt-4 grid grid-cols-2 gap-4">
          <div
            class="flex items-center text-sm text-gray-600 dark:text-gray-400"
          >
            <svg
              class="flex-shrink-0 mr-2 h-4 w-4 text-gray-500 dark:text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            {{ application.applicationDate | date : "dd/MM/yyyy" }}
          </div>
          <div class="flex items-center justify-end">
            <button
              class="flex items-center gap-1 rounded-lg bg-blue-50 px-3 py-1.5 text-xs font-medium text-blue-600 transition hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
            >
              <svg
                class="h-3.5 w-3.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              Voir les détails
            </button>
          </div>
        </div>
      </a>
    </div>
  </div>
</div>
