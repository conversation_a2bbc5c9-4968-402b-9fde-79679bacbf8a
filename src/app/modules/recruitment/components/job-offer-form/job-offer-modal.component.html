<div
  class="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto overflow-x-hidden bg-gray-900/50 p-4"
>
  <div
    class="relative max-h-[90vh] w-full max-w-md overflow-hidden rounded-xl bg-white shadow-xl dark:bg-gray-800"
  >
    <!-- Header -->
    <div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        {{
          isEditing ? "Modifier l'offre d'emploi" : "Créer une offre d'emploi"
        }}
      </h3>
    </div>

    <!-- Body -->
    <div class="max-h-[60vh] overflow-y-auto px-6 py-4">
      <form [formGroup]="offerForm" (ngSubmit)="onSubmit()">
        <!-- Title -->
        <div class="mb-4">
          <label
            for="title"
            class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Titre <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="title"
            formControlName="title"
            class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
            placeholder="Ex: Développeur Web Frontend"
          />
          <div
            *ngIf="
              offerForm.get('title')?.invalid &&
              (offerForm.get('title')?.dirty || offerForm.get('title')?.touched)
            "
            class="mt-1 text-xs text-red-500"
          >
            <div *ngIf="offerForm.get('title')?.errors?.['required']">
              Le titre est requis.
            </div>
          </div>
        </div>

        <!-- Department -->
        <div class="mb-4">
          <label
            for="departmentId"
            class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Département
          </label>
          <select
            id="departmentId"
            formControlName="departmentId"
            class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
          >
            <option value="" disabled>Sélectionnez un département</option>
            <option *ngFor="let dept of departments" [value]="dept.id">
              {{ dept.departmentName }}
            </option>
          </select>
        </div>

        <!-- Position -->
        <div class="mb-4">
          <label
            for="positionId"
            class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Poste
          </label>
          <select
            id="positionId"
            formControlName="positionId"
            class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
          >
            <option value="" disabled>Sélectionnez un poste</option>
            <option *ngFor="let pos of positions" [value]="pos.id">
              {{ pos.positionTitle }}
            </option>
          </select>
        </div>

        <!-- Description -->
        <div class="mb-4">
          <label
            for="description"
            class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Description <span class="text-red-500">*</span>
          </label>
          <textarea
            id="description"
            formControlName="description"
            rows="4"
            class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
            placeholder="Décrivez l'offre d'emploi en détail..."
          ></textarea>
          <div
            *ngIf="
              offerForm.get('description')?.invalid &&
              (offerForm.get('description')?.dirty ||
                offerForm.get('description')?.touched)
            "
            class="mt-1 text-xs text-red-500"
          >
            <div *ngIf="offerForm.get('description')?.errors?.['required']">
              La description est requise.
            </div>
          </div>
        </div>

        <!-- Required Skills -->
        <div class="mb-4">
          <label
            class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Compétences requises
          </label>
          <div class="mb-2 flex flex-wrap gap-2">
            <div
              *ngFor="let skill of requiredSkills"
              class="flex items-center rounded-full bg-blue-50 px-3 py-1 text-sm text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
            >
              <span>{{ skill }}</span>
              <button
                type="button"
                (click)="removeSkill(skill)"
                class="ml-1.5 text-blue-700 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div class="flex">
            <input
              type="text"
              #skillInput
              (keydown.enter)="
                addSkill(skillInput.value);
                skillInput.value = '';
                $event.preventDefault()
              "
              class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
              placeholder="Saisissez une compétence et appuyez sur Entrée"
            />
            <button
              type="button"
              (click)="addSkill(skillInput.value); skillInput.value = ''"
              class="ml-2 flex items-center justify-center rounded-lg bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- Contract Types -->
        <div class="mb-4">
          <label
            class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Types de contrat <span class="text-red-500">*</span>
          </label>
          <div class="flex flex-wrap gap-2">
            <button
              *ngFor="let type of contractTypes"
              type="button"
              (click)="toggleContractType(type)"
              [class.bg-blue-600]="isContractTypeSelected(type)"
              [class.text-white]="isContractTypeSelected(type)"
              class="rounded-lg border border-gray-300 px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              {{ type }}
            </button>
          </div>
          <div
            *ngIf="
              offerForm.get('contractTypes')?.invalid &&
              (offerForm.get('contractTypes')?.dirty ||
                offerForm.get('contractTypes')?.touched)
            "
            class="mt-1 text-xs text-red-500"
          >
            <div *ngIf="offerForm.get('contractTypes')?.errors?.['required']">
              Au moins un type de contrat est requis.
            </div>
          </div>
        </div>

        <!-- Dates Grid -->
        <div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
          <!-- Publish Date -->
          <div>
            <label
              for="publishDate"
              class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Date de publication <span class="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="publishDate"
              formControlName="publishDate"
              class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
            />
            <div
              *ngIf="
                offerForm.get('publishDate')?.invalid &&
                (offerForm.get('publishDate')?.dirty ||
                  offerForm.get('publishDate')?.touched)
              "
              class="mt-1 text-xs text-red-500"
            >
              <div *ngIf="offerForm.get('publishDate')?.errors?.['required']">
                La date de publication est requise.
              </div>
            </div>
          </div>

          <!-- Expiration Date -->
          <div>
            <label
              for="expirationDate"
              class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Date d'expiration <span class="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="expirationDate"
              formControlName="expirationDate"
              class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
            />
            <div
              *ngIf="
                offerForm.get('expirationDate')?.invalid &&
                (offerForm.get('expirationDate')?.dirty ||
                  offerForm.get('expirationDate')?.touched)
              "
              class="mt-1 text-xs text-red-500"
            >
              <div
                *ngIf="offerForm.get('expirationDate')?.errors?.['required']"
              >
                La date d'expiration est requise.
              </div>
            </div>
          </div>
        </div>

        <!-- Location -->
        <div class="mb-4">
          <label
            for="location"
            class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Lieu <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="location"
            formControlName="location"
            class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
            placeholder="Ex: Paris, France"
          />
          <div
            *ngIf="
              offerForm.get('location')?.invalid &&
              (offerForm.get('location')?.dirty ||
                offerForm.get('location')?.touched)
            "
            class="mt-1 text-xs text-red-500"
          >
            <div *ngIf="offerForm.get('location')?.errors?.['required']">
              Le lieu est requis.
            </div>
          </div>
        </div>

        <!-- Salary Range -->
        <div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label
              for="minSalary"
              class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Salaire minimum
            </label>
            <input
              type="number"
              id="minSalary"
              formControlName="minSalary"
              class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
              placeholder="Ex: 30000"
            />
          </div>
          <div>
            <label
              for="maxSalary"
              class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Salaire maximum
            </label>
            <input
              type="number"
              id="maxSalary"
              formControlName="maxSalary"
              class="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"
              placeholder="Ex: 45000"
            />
          </div>
        </div>

        <!-- Status -->
        <div class="mb-4">
          <label
            class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
            >Statut</label
          >
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <input
                type="radio"
                id="statusActive"
                formControlName="status"
                [value]="JobOfferStatusEnum.ACTIVE"
                class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"
              />
              <label
                for="statusActive"
                class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Actif</label
              >
            </div>
            <div class="flex items-center">
              <input
                type="radio"
                id="statusDraft"
                formControlName="status"
                [value]="JobOfferStatusEnum.DRAFT"
                class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"
              />
              <label
                for="statusDraft"
                class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Brouillon</label
              >
            </div>
            <div class="flex items-center">
              <input
                type="radio"
                id="statusClosed"
                formControlName="status"
                [value]="JobOfferStatusEnum.EXPIRED"
                class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"
              />
              <label
                for="statusClosed"
                class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Fermé</label
              >
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Footer -->
    <div class="border-t border-gray-200 px-6 py-4 dark:border-gray-700">
      <div class="flex flex-wrap items-center justify-end gap-3">
        <button
          type="button"
          (click)="close.emit()"
          class="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          Annuler
        </button>
        <button
          type="button"
          (click)="onSubmit()"
          [disabled]="offerForm.invalid || isSubmitting"
          class="flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-70 dark:bg-blue-700 dark:hover:bg-blue-800"
        >
          <svg
            *ngIf="isSubmitting"
            class="mr-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          {{ isEditing ? "Mettre à jour" : "Créer" }}
        </button>
      </div>
    </div>
  </div>
</div>
